import { NgStyle } from '@angular/common';
import { Component, Input, TemplateRef, input } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import {
  NzFormControlComponent,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzInputDirective, NzInputGroupComponent } from 'ng-zorro-antd/input';

@Component({
  selector: 'app-input-generic',
  standalone: true,
  imports: [
    NgStyle,
    FormsModule,
    ReactiveFormsModule,
    NzFormItemComponent,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzInputGroupComponent,
    NzInputDirective,
    NzColDirective,
    NzRowDirective,
    TranslateModule,
  ],
  templateUrl: './input-generic.html',
  styleUrl: './input-generic.less',
})
export class InputGenericComponent {
  readonly parentForm = input<FormGroup | any>(undefined);
  readonly controlName = input<string>(undefined);
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() label: string;
  readonly placeholder = input<string>('');
  readonly pattern = input<string>(undefined);
  readonly type = input<'text' | 'email'>('text');
  readonly prefixIcon = input<string>(undefined);
  readonly suffixIcon = input<string>(undefined);
  readonly minLength = input<number>(0);
  readonly maxLength = input<number>(999);
  readonly labelPosition = input<'left' | 'top'>('left');
  readonly style = input<{
    [key: string]: any;
  }>(undefined);
  readonly showErrorText = input<boolean>(true);
  readonly suffixAddonText = input<string | TemplateRef<any>>(undefined);
  readonly prefixAddonText = input<string | TemplateRef<any>>(undefined);
  readonly customErrors = input<string[]>(undefined);

  isRequired() {
    return this.parentForm()
      .get(this.controlName())
      .hasValidator(Validators.required);
  }

  getCombinedStyles(): { [key: string]: any } {
    return {
      ...this.style(),
      display: this.labelPosition() === 'left' ? 'flex' : 'block',
    };
  }
}
