import { Routes } from '@angular/router';
import { loginGuard } from '@core/guards/login-guard';
import { registerGuard } from '@core/guards/register-guard';
import { ActivateAccountComponent } from './activate-account/activate-account';
import { AuthComponent } from './auth';
import { ForgotPasswordComponent } from './forgot-password/forgot-password';
import { LoginComponent } from './login/login';
import { RegisterComponent } from './register/register';
import { ResetPasswordComponent } from './reset-password/reset-password';

export const AUTH_ROUTES: Routes = [
  {
    path: '',
    component: AuthComponent,
    children: [
      { path: '', redirectTo: 'login', pathMatch: 'full' },
      {
        path: 'login',
        component: LoginComponent,
        canActivate: [loginGuard],
      },
      {
        path: 'reset-password',
        component: ResetPasswordComponent,
      },
      {
        path: 'forgot-password',
        component: ForgotPasswordComponent,
      },
      {
        path: 'activate-account',
        component: ActivateAccountComponent,
      },
      {
        path: 'register',
        component: RegisterComponent,
        canActivate: [loginGuard, registerGuard],
      },
    ],
  },
];
