/**Session Storage Utils */
export class SessionStorageUtils {
  /**Sets the Object identified by key creating a new key/Object pair if none existed for key previously.
   * @param name object name
   * @param value
   */
  public static setObject(name: string, value: any) {
    sessionStorage.setItem(name, JSON.stringify(value));
  }

  /**Sets the Object property value
   * @param name object name
   * @param property
   * @param value
   */
  public static setObjectProperty(name: string, property: string, value: any) {
    sessionStorage.setItem(
      name,
      JSON.stringify(
        (JSON.parse(<any>sessionStorage.getItem(name))[property] = value)
      )
    );
  }

  /**Returns the current Object associated with the given key, or null if the given key does not exist in the list associated with the object.
   * @param key
   * @returns JavaScript Object Notation (JSON) string into an object
   */
  public static getObject(key: string) {
    return JSON.parse(<any>sessionStorage.getItem(key));
  }

  /**Returns the current Object Property Value associated with the given key, or null if the given key does not exist in the list associated with the object.
   * @param name object name
   * @param property
   * @returns JavaScript Object Notation (JSON) string into an object property.
   */
  public static getObjectProperty(name: string, property: string): any {
    return JSON.parse(<any>sessionStorage.getItem(name))[property];
  }
}

/**Local Storage Utils */
export class LocalStorageUtils {
  /**Sets the Object identified by key creating a new key/Object pair if none existed for key previously.
   * @param key
   * @param value
   */
  public static setObject(key: string, value: any) {
    localStorage.setItem(key, JSON.stringify(value));
  }

  /**Sets the Object property valuje by key
   * @param name object name
   * @param property property name in object
   * @param value
   */
  public static setObjectProperty(name: string, property: string, value: any) {
    localStorage.setItem(
      name,
      JSON.stringify(
        (JSON.parse(<any>sessionStorage.getItem(name))[property] = value)
      )
    );
  }

  /**Returns the current Object Property Value associated with the given key, or null if the given key does not exist in the list associated with the object.
   * @param key
   * @param property
   * @returns JavaScript Object Notation (JSON) string into an object property.
   */
  public static getObjectProperty(key: string, property: string) {
    return JSON.parse(<any>localStorage.getItem(key))[property];
  }

  /**Returns the current Object associated with the given key, or null if the given key does not exist in the list associated with the object.
   * @param key
   * @returns JavaScript Object Notation (JSON) string into an object
   */
  public static getObject(key: string) {
    return JSON.parse(<any>localStorage.getItem(key));
  }
}
