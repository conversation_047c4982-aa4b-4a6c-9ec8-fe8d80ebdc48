import { Injectable } from '@angular/core';
import { PreloadingStrategy, Route } from '@angular/router';

import { Observable, of } from 'rxjs';

/**
 * PreloadingStrategy personalizzata che carica i moduli solo se la route ha il flag 'preload' a true.
 */
@Injectable({ providedIn: 'root' })
export class FlagBasedPreloadingStrategy extends PreloadingStrategy {
  // 👇 For clarity, I prefer `load` rather than `fn` for the callback name
  preload(route: Route, load: () => Observable<any>): Observable<any> {
    return route.data?.['preload'] === true ? load() : of(null);
  }
}
