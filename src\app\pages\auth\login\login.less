@import 'mixin';

.themeMixin({
  :host {


    .container {
      width: 340px;

      .subtitle {
        margin-bottom: 24px !important
      }

      .login-form {
        min-width: 300px;
        margin-top: 24px;

        &-forgot {
          text-align: end;
        }
      }

      .login-form-margin {
        margin-bottom: 24px;
      }


      [nz-button] {
        width: 100%;
      }

      h1 {
        margin-bottom: 0;
      }

      h6 {
        margin-bottom: 3rem;
        color: @medium_grey;
      }

      .divider {
        margin-bottom: 16px;
      }

    }
  }
});

.error-message {
  padding: 8px 0;
  text-align: center;
}