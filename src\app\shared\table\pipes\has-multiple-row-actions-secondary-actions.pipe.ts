import { Pipe, PipeTransform } from '@angular/core';
import { CheckUtils } from '@core/utils/check';
import { ITableRowAction } from '../types/table.action';

@Pipe({
  name: 'hasMultipleRowActionsSecondaryActions',
  standalone: true
})
export class HasMultipleRowActionsSecondaryActionsPipe
  implements PipeTransform
{
  transform(rowActions: ITableRowAction[]): unknown {
    return (
      CheckUtils.isNotNilArray(rowActions) &&
      rowActions.find(mr => mr.showAsSecondaryAction === true) != undefined
    );
  }
}
