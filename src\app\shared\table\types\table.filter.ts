import { requestFilterOperatorType } from './table.column';

/** Define the table quick search item */
export interface ITableFilterItem {
  key: string;
  value: any;
  visible?: boolean;
  byDefault?: boolean;
  filterType?: tableFilterType;
  filterTypeof?: string;
  filterOperator?: requestFilterOperatorType | requestFilterOperatorType[];
}

export enum tableFilterType {
  search,
  radio,
  multiple,
  date,
  singleDate,
  custom
}

export interface IColumnFilter {
  key: any;
  value: any;
  behavior?: IColumnBehavior;
}
/**Define the IColumnBehavior object used for set column status */
export interface IColumnBehavior {
  readonly: boolean;
  visible: boolean;
}

/** Define the Enumeration For Date range Shortcut */
export enum dateRangeType {
  last24Hour = 'last24Hour',
  lastWeek = 'lastWeek',
  lastMonth = 'lastMonth',
  lastYear = 'lastYear'
}

/** Define the Enumeration For Date range time in milliseconds */
export enum dateRangeNumberType {
  oneDay = 86400000,
  oneWeek = 604800000,
  oneMonth = 2628000000,
  oneYear = 31540000000
}
