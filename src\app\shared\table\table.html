<div class="table">
  <div nz-row nzJustify="space-around" nzAlign="middle" class="top-row">
    <div nz-col [nzSpan]="12">
      <app-active-filters
        [columns]="tableLayoutSettings.listOfColumns"
        [maxActiveFilterTagCount]="maxActiveFilterTagCount()"
        style="display: inline-block"
      >
      </app-active-filters>
    </div>
    <div nz-col [nzSpan]="12">
      <div nz-row nzJustify="end" nzAlign="middle">
        <div nz-col [nzSpan]="24" style="text-align: right">
          <nz-space [nzSize]="8" style="height: 24px">
            @if (refresh) {
              <div *nzSpaceItem>
                {{ "TABLE.refresh" | translate }}:
                <span style="font-weight: 600">
                  {{
                    refresh.interval > 0
                      ? refresh.interval + " sec."
                      : ("TABLE.no" | translate)
                  }}
                </span>
              </div>
              <div *nzSpaceItem style="width: 150px">
                <nz-slider
                  [nzMin]="refresh.min"
                  [nzMax]="refresh.max"
                  [nzStep]="refresh.step"
                  [ngModel]="refresh.interval"
                  (ngModelChange)="changeRefreshInterval($event)"
                >
                </nz-slider>
              </div>
            }
            <app-table-column-config
              *nzSpaceItem
              [(listOfColumns)]="tableLayoutSettings.listOfColumns"
              [tableName]="tableId()"
              (resetColumnEvent)="resetColumns()"
            >
            </app-table-column-config>
            @if (refresh) {
              <button
                *nzSpaceItem
                [nzTooltipTitle]="'TABLE.reloadData' | translate"
                nzTooltipPlacement="left"
                nz-button
                nz-tooltip
                nzSize="small"
                (click)="refreshData()"
              >
                <i nz-icon nzType="reload" nzTheme="outline"></i>
              </button>
            }
          </nz-space>
        </div>
      </div>
    </div>
  </div>
</div>

<div>
  <nz-table
    #virtualTable
    [nzVirtualItemSize]="rowSize()"
    [nzData]="data()"
    [nzVirtualForTrackBy]="trackByIndex"
    [nzFrontPagination]="false"
    [nzBordered]="true"
    [nzLoading]="loading()"
    nzSize="small"
    [nzTotal]="tableLayoutSettings.pagination.total"
    [(nzPageSize)]="tableLayoutSettings.pagination.pageSize"
    [nzPageIndex]="tableLayoutSettings.pagination.pageIndex"
    nzShowSizeChanger
    (nzQueryParams)="onQueryParamsChange($event)"
    [nzPageSizeOptions]="paginationOptions()"
    [nzShowTotal]="tplTotal"
    (nzCurrentPageDataChange)="onPageDataChange($event)"
    [nzScroll]="{ x: tableXAxis(), y: tableYAxis }"
  >
    <thead>
      <tr>
        @if (tableLayoutSettings.showRowSelection) {
          <th
            [nzChecked]="checked()"
            [nzIndeterminate]="indeterminate()"
            [nzDisabled]="!allRowsSelection()"
            (nzCheckedChange)="onAllChecked($event)"
            [nzWidth]="'50px'"
            [nzLeft]="true"
          ></th>
        }
        @for (
          column of tableLayoutSettings.listOfColumns;
          track trackByColumn(colIndex, column);
          let colIndex = $index
        ) {
          @if (column.visible) {
            <th
              [nzColumnKey]="column.id"
              [nzSortOrder]="column.sortOrder"
              [nzSortFn]="column.sortFn"
              [nzSortDirections]="column.sortDirections"
              [nzWidth]="column.width"
              [nzLeft]="column.lockOnLeft"
              [nzRight]="column.lockOnRight"
              [nzAlign]="column.alignment"
              [nzCustomFilter]="column | hasColumnFilter"
            >
              {{ column.title | translate }}
              @if (
                (column | hasColumnFilter) && customSearchItems().length > 0
              ) {
                <nz-filter-trigger
                  [(nzVisible)]="customSearchItems()[colIndex].visible"
                  [nzActive]="customSearchItems()[colIndex].value != undefined"
                  [nzDropdownMenu]="customSearchMenu"
                >
                  <i
                    nz-icon
                    [nzType]="column | tableFilterIcon"
                    [nzTheme]="column.filterFn ? 'fill' : 'outline'"
                  ></i>
                </nz-filter-trigger>
              }
              <nz-dropdown-menu #customSearchMenu="nzDropdownMenu">
                <div class="ant-table-filter-dropdown">
                  @if (column.hasSearchFilter) {
                    <div class="filter-box">
                      <th-search-filter
                        [column]="column"
                        [searchValue]="customSearchItems()[colIndex].value"
                        (onSetCustomSearchItem)="
                          setCustomSearchItem($event, column.id)
                        "
                        (onCustomSearch)="onCustomSearch()"
                        (onResetSearch)="customSearchReset(column)"
                      >
                      </th-search-filter>
                    </div>
                  }
                  @if (column.hasDateFilter || column.hasSingleDateFilter) {
                    <div class="filter-box">
                      <th-date-filter
                        [column]="column"
                        [searchValue]="customSearchItems()[colIndex].value"
                        (onDateChange)="setDateSearchItem($event, column.id)"
                        (onResetDate)="customSearchReset(column)"
                      >
                      </th-date-filter>
                    </div>
                  }
                  @if (column.filterFn) {
                    <th-multiple-filter
                      [listOfFilter]="column.listOfFilter"
                      [filtersSearchable]="column.filtersSearchable"
                      [filterMultiple]="column.filterMultiple"
                      (onFilterChange)="onFilterChange($event, column.id)"
                    >
                    </th-multiple-filter>
                  }
                </div>
              </nz-dropdown-menu>
            </th>
          }
        }
        @if (tableLayoutSettings.singleRowActions.length > 0) {
          <th [nzWidth]="'80px'" style="text-align: center" nzRight>
            {{ "TABLE.actions" | translate }}
          </th>
        }
      </tr>
    </thead>
    <tbody>
      <ng-template nz-virtual-scroll let-data let-index="index">
        <tr>
          @if (tableLayoutSettings.showRowSelection) {
            <td
              [nzChecked]="mapOfCheckedItem().has(data.index)"
              [nzDisabled]="
                disableSelectionFn(
                  tableLayoutSettings.rowDisableSelectionFn,
                  data
                )
              "
              (nzCheckedChange)="onRowChecked(data.index, $event, data)"
              nzLeft
            ></td>
          }
          @for (
            column of tableLayoutSettings.listOfColumns;
            track trackByColumn($index, column)
          ) {
            @if (column.cellTemplate) {
              @if (column.visible) {
                <td
                  class="{{ column.alignment }}"
                  [ngStyle]="{ height: rowSize() + 'px' }"
                  [nzLeft]="column.lockOnLeft"
                  [nzRight]="column.lockOnRight"
                >
                  <ng-template
                    [ngTemplateOutletContext]="{
                      element: (data | getCellValue: column.id),
                      row: data,
                      column: column,
                    }"
                    [ngTemplateOutlet]="column.cellTemplate"
                  >
                  </ng-template>
                </td>
              }
            }
            @if (!column.cellTemplate) {
              @if (column.visible) {
                <td
                  class="{{ column.alignment }}"
                  [nzLeft]="column.lockOnLeft"
                  [nzRight]="column.lockOnRight"
                  [ngStyle]="{ height: rowSize() + 'px' }"
                >
                  @if (!!column.formatValueFn) {
                    <span>
                      {{
                        column.formatValueFn(data | getCellValue: column.id)
                          | nullValue
                      }}
                    </span>
                  }
                  @if (!column.formatValueFn) {
                    <span>
                      {{ data | getCellValue: column.id | nullValue }}
                    </span>
                  }
                </td>
              }
            }
          }
          @if (tableLayoutSettings.singleRowActions.length > 0) {
            <td style="text-align: center" nzRight>
              <a
                [ngClass]="[
                  'row-action',
                  menu_single_row_actions.childElementCount == 0
                    ? 'not-allowed'
                    : 'allowed',
                ]"
                nz-dropdown
                [nzDropdownMenu]="menu_actions"
                [nzDisabled]="menu_single_row_actions.childElementCount == 0"
                [nzPlacement]="'bottomRight'"
              >
                <i
                  nz-icon
                  [nzType]="
                    menu_single_row_actions.childElementCount != 0
                      ? 'more'
                      : 'stop'
                  "
                  nzTheme="outline"
                ></i>
              </a>
              <nz-dropdown-menu #menu_actions="nzDropdownMenu">
                <ul nz-menu #menu_single_row_actions>
                  @for (
                    singleRowAct of tableLayoutSettings.singleRowActions;
                    track singleRowAct
                  ) {
                    @if (
                      !singleRowAct.hidden &&
                      (singleRowAct | isSingleRowActionVisible: data) &&
                      !singleRowAct.children
                    ) {
                      <li
                        nz-menu-item
                        (click)="onSingleRowActionClick(singleRowAct, data)"
                        [nzDisabled]="singleRowAct.disabled"
                        [nzDanger]="singleRowAct.danger"
                      >
                        <i nz-icon [nzType]="singleRowAct.icon"></i>
                        <span class="ml-8">{{
                          singleRowAct.label | translate
                        }}</span>
                      </li>
                    } @else {
                      @if (
                        !singleRowAct.hidden &&
                        (singleRowAct | isSingleRowActionVisible: data)
                      ) {
                        <li nz-submenu [nzTitle]="tplSingleRowTitle">
                          <ng-template #tplSingleRowTitle>
                            <i nz-icon [nzType]="singleRowAct.icon"></i>
                            <span class="ml-8">{{
                              singleRowAct.label | translate
                            }}</span>
                          </ng-template>
                          <ul>
                            @for (
                              childRowAct of singleRowAct.children;
                              track childRowAct
                            ) {
                              @if (
                                !childRowAct.hidden &&
                                (childRowAct | isSingleRowActionVisible: data)
                              ) {
                                <li
                                  nz-menu-item
                                  (click)="
                                    onSingleRowActionClick(childRowAct, data)
                                  "
                                  [nzDisabled]="childRowAct.disabled"
                                  [nzDanger]="childRowAct.danger"
                                >
                                  @if (childRowAct.icon) {
                                    <i nz-icon [nzType]="childRowAct.icon"></i>
                                  }
                                  <span class="ml-8">{{
                                    childRowAct.label | translate
                                  }}</span>
                                </li>
                              }
                            }
                          </ul>
                        </li>
                      }
                    }
                  }
                </ul>
              </nz-dropdown-menu>
            </td>
          }
        </tr>
      </ng-template>
    </tbody>
  </nz-table>
  <ng-template #tplTotal let-total
    >{{ tableLayoutSettings.pagination.total }}
    {{ "TABLE.totals" | translate }}
  </ng-template>
</div>

<!-- ***** DRAWER FOR MULTISELECTABLE ROWS *****-->
<nz-drawer
  [nzClosable]="false"
  [nzMask]="true"
  [nzVisible]="mapOfCheckedItem().size > 0"
  [nzPlacement]="'bottom'"
  drawerMask
  [mask]="false"
  [drawerPosition]="'bottom'"
  [drawerVisibile]="mapOfCheckedItem().size > 0"
  [nzHeight]="'50px'"
  [nzBodyStyle]="{ padding: 0 }"
  (nzOnClose)="onCheckDrawerClose()"
>
  <ng-container *nzDrawerContent>
    <div nz-row nzJustify="center" nzAlign="middle" style="height: 50px">
      <!--Left Side: Item(s) selected count-->
      <div nz-col [nzSpan]="8" style="padding-left: 36px">
        <nz-space [nzSize]="8">
          @if (allRowsSelection()) {
            <div *nzSpaceItem style="padding-top: 5px">
              @if (mapOfCheckedItem().size < data().length) {
                <a
                  class="menu-check"
                  nz-tooltip
                  nzTooltipTitle="Select all"
                  nzTooltipPlacement="top"
                  (click)="onAllChecked(true)"
                >
                  <i
                    nz-icon
                    nzType="check-circle"
                    nzTheme="outline"
                    style="font-size: 18px"
                  ></i>
                </a>
              }
              @if (mapOfCheckedItem().size == data().length) {
                <a
                  class="menu-check"
                  nz-tooltip
                  nzTooltipTitle="Clear"
                  nzTooltipPlacement="top"
                  (click)="onAllChecked(false)"
                >
                  <i
                    nz-icon
                    nzType="minus-circle"
                    nzTheme="outline"
                    style="font-size: 18px"
                  ></i>
                </a>
              }
            </div>
          }
          <div *nzSpaceItem>
            <nz-tag style="border-radius: 15px"
              >{{ mapOfCheckedItem().size }}
            </nz-tag>
            <span class="column-item">{{
              "TABLE.itemsSelected" | translate
            }}</span>
          </div>
        </nz-space>
      </div>
      <!--Center Side: Menu-->
      <div nz-col [nzSpan]="8" style="text-align: center">
        <nz-space [nzSize]="24">
          @if (drawerConfig && drawerConfig.centerTemplate) {
            <ng-container
              [ngTemplateOutlet]="drawerConfig.centerTemplate"
            ></ng-container>
          } @else {
            @for (
              multiRowAct of tableLayoutSettings.multipleRowActions;
              track multiRowAct
            ) {
              @if (!multiRowAct.showAsSecondaryAction) {
                <a
                  *nzSpaceItem
                  class="menu-check"
                  nz-tooltip
                  [nzTooltipTitle]="multiRowAct.label | translate"
                  nzTooltipPlacement="top"
                  (click)="onMultipleRowActionClick(multiRowAct)"
                >
                  <i
                    nz-icon
                    [nzType]="multiRowAct.icon"
                    nzTheme="outline"
                    style="font-size: 20px"
                  ></i>
                </a>
              }
            }
          }
          @if (
            tableLayoutSettings.multipleRowActions
              | hasMultipleRowActionsSecondaryActions
          ) {
            <div *nzSpaceItem>
              <a
                class="menu-check"
                nz-dropdown
                [nzDropdownMenu]="menu_check_other"
                [nzPlacement]="'topCenter'"
              >
                <i
                  nz-icon
                  nzType="ellipsis"
                  nzTheme="outline"
                  style="font-size: 20px"
                ></i>
              </a>
              <nz-dropdown-menu #menu_check_other="nzDropdownMenu">
                <ul nz-menu>
                  @for (
                    multiRowAct of tableLayoutSettings.multipleRowActions;
                    track multiRowAct
                  ) {
                    @if (multiRowAct.showAsSecondaryAction) {
                      <li
                        nz-menu-item
                        (click)="onMultipleRowActionClick(multiRowAct)"
                      >
                        <i
                          nz-icon
                          [nzType]="multiRowAct.icon"
                          nzTheme="outline"
                          style="font-size: 20px"
                        ></i>
                        {{ multiRowAct.label | translate }}
                      </li>
                    }
                  }
                </ul>
              </nz-dropdown-menu>
            </div>
          }
        </nz-space>
      </div>
      <!--Right Side: Menu-->
      <div
        nz-col
        [nzSpan]="8"
        style="width: 100%; text-align: right; padding-right: 36px"
      >
        <ng-container
          [ngTemplateOutlet]="drawerConfig?.rightTemplate"
        ></ng-container>
      </div>
    </div>
  </ng-container>
</nz-drawer>
