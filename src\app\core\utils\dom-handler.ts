/** Dom Elements Utils */
export class DomHandler {
  public static addClass(element: any, className: string): void {
    if (element.classList) element.classList.add(className);
    else element.className += ' ' + className;
  }

  public static removeClass(element: any, className: string): void {
    if (element && element.classList) element.classList.remove(className);
    else
      element.className = element.className.replace(
        new RegExp(
          '(^|\\b)' + className.split(' ').join('|') + '(\\b|$)',
          'gi'
        ),
        ' '
      );
  }

  public static hasClass(element: any, className: string): boolean {
    if (element.classList) return element.classList.contains(className);
    else
      return new RegExp('(^| )' + className + '( |$)', 'gi').test(
        element.className
      );
  }

  public static findSingle(element: any, selector: string): any {
    if (element) {
      return element.querySelector(selector);
    }
    return null;
  }

  public static getOuterHeight(el: any, margin?: any) {
    let height = el.offsetHeight;

    if (margin) {
      let style = getComputedStyle(el);
      height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);
    }

    return height;
  }

  public static scrollInView(container: any, item: any) {
    let borderTopValue: string =
      getComputedStyle(container).getPropertyValue('borderTopWidth');
    let borderTop: number = borderTopValue ? parseFloat(borderTopValue) : 0;
    let paddingTopValue: string =
      getComputedStyle(container).getPropertyValue('paddingTop');
    let paddingTop: number = paddingTopValue ? parseFloat(paddingTopValue) : 0;
    let containerRect = container.getBoundingClientRect();
    let itemRect = item.getBoundingClientRect();
    let offset =
      itemRect.top +
      document.body.scrollTop -
      (containerRect.top + document.body.scrollTop) -
      borderTop -
      paddingTop;
    let scroll = container.scrollTop;
    let elementHeight = container.clientHeight;
    let itemHeight = this.getOuterHeight(item);

    if (offset < 0) {
      container.scrollTop = scroll + offset;
    } else if (offset + itemHeight > elementHeight) {
      container.scrollTop = scroll + offset - elementHeight + itemHeight;
    }
  }

  public static fadeOut(element: any, ms: any) {
    var opacity = 1,
      interval = 50,
      duration = ms,
      gap = interval / duration;

    let fading = setInterval(() => {
      opacity = opacity - gap;

      if (opacity <= 0) {
        opacity = 0;
        clearInterval(fading);
      }

      element.style.opacity = opacity;
    }, interval);
  }

  public static getWindowScrollTop(): number {
    let doc = document.documentElement;
    return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);
  }

  public static getWindowScrollLeft(): number {
    let doc = document.documentElement;
    return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);
  }

  public static measureScrollbar(
    direction: 'vertical' | 'horizontal' = 'vertical',
    prefix: string = 'ant'
  ): number {
    let scrollbarVerticalSize!: number;
    let scrollbarHorizontalSize!: number;
    // Measure scrollbar width for padding body during modal show/hide
    const scrollbarMeasure = {
      position: 'absolute',
      top: '-9999px',
      width: '50px',
      height: '50px'
    };

    if (typeof document === 'undefined' || typeof window === 'undefined') {
      return 0;
    }
    const isVertical = direction === 'vertical';

    if (isVertical && scrollbarVerticalSize) {
      return scrollbarVerticalSize;
    } else if (!isVertical && scrollbarHorizontalSize) {
      return scrollbarHorizontalSize;
    }
    const scrollDiv = document.createElement('div');
    Object.keys(scrollbarMeasure).forEach(scrollProp => {
      // @ts-ignore
      scrollDiv.style[scrollProp] = scrollbarMeasure[scrollProp];
    });
    // apply hide scrollbar className ahead
    scrollDiv.className = `${prefix}-hide-scrollbar scroll-div-append-to-body`;
    // Append related overflow style
    if (isVertical) {
      scrollDiv.style.overflowY = 'scroll';
    } else {
      scrollDiv.style.overflowX = 'scroll';
    }
    document.body.appendChild(scrollDiv);
    let size: number;
    if (isVertical) {
      size = scrollDiv.offsetWidth - scrollDiv.clientWidth;
      scrollbarVerticalSize = size;
    } else {
      size = scrollDiv.offsetHeight - scrollDiv.clientHeight;
      scrollbarHorizontalSize = size;
    }

    document.body.removeChild(scrollDiv);
    return size;
  }

  public static goOutFullscreen() {
    if (document.exitFullscreen) document.exitFullscreen();
    // else if (document.mozCancelFullScreen) document.mozCancelFullScreen();
    // else if (document.webkitExitFullscreen) document.webkitExitFullscreen();
    // else if (document.msExitFullscreen) document.msExitFullscreen();
  }

  public static goInFullscreen(element: any) {
    let elem: any = document.documentElement;
    let methodToBeInvoked =
      elem.requestFullscreen ||
      elem.webkitRequestFullScreen ||
      elem['mozRequestFullscreen'] ||
      elem['msRequestFullscreen'];
    if (methodToBeInvoked) methodToBeInvoked.call(elem);
    if (element.requestFullscreen) element.requestFullscreen();
  }
}
