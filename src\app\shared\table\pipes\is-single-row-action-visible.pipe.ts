import { Pipe, PipeTransform } from '@angular/core';
import { CheckUtils } from '@core/utils/check';
import { ITableRowAction } from '../types/table.action';

@Pipe({
  name: 'isSingleRowActionVisible',
  standalone: true
})
export class IsSingleRowActionVisiblePipe implements PipeTransform {
  transform(rowAction: ITableRowAction, row?: any): boolean {
    if (CheckUtils.isNullUndefinedOrEmpty(rowAction.visibilityFn)) return true;
    else return rowAction.visibilityFn(row);
  }
}
