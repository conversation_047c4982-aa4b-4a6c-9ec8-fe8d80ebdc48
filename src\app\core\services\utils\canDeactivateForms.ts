import { inject } from '@angular/core';
import { AbstractControl, FormArray, FormGroup } from '@angular/forms';
import { CanDeactivateFn } from '@angular/router';
import { ModalService } from './modal';

/**
 * Guard per la protezione della navigazione se il form contiene modifiche non salvate.
 * Mostra un modal di conferma se il form o un FormArray è dirty.
 * @param getFormFn Funzione che restituisce il FormGroup dal componente
 * @returns CanDeactivateFn che mostra un modal se ci sono modifiche
 */
export const canDeactivateFormGuardService = (
  getFormFn: (component: any) => FormGroup,
): CanDeactivateFn<any> => {
  return (component: any) => {
    const modalService = inject(ModalService);
    const form = getFormFn(component);

    const hasModifiedFormArray = Object.values(form.controls).some(
      (control: AbstractControl) =>
        control instanceof FormArray && control.dirty,
    );

    if (form.dirty || hasModifiedFormArray) {
      return modalService.confirmDeleteCanDeactivate(
        'canDeactivateTitle',
        'canDeactivateSubtitle',
      );
    }

    return true;
  };
};
