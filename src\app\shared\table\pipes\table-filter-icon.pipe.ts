import { Pipe, PipeTransform } from '@angular/core';
import { ITableColumn } from '../types/table.column';

@Pipe({
  name: 'tableFilterIcon',
  standalone: true
})
export class TableFilterIconPipe implements PipeTransform {
  transform(column: ITableColumn): string {
    switch (true) {
      case column.hasDateFilter:
      case column.hasSingleDateFilter:
        return 'calendar';
      case column.hasSearchFilter:
        return 'search';
      default:
        return 'filter';
    }
  }
}
