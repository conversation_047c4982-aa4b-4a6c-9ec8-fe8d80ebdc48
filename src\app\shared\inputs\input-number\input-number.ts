import { Ng<PERSON><PERSON> } from '@angular/common';
import { Component, Input, input } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import {
  NzFormControlComponent,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzInputNumberComponent } from 'ng-zorro-antd/input-number';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-input-number',
  standalone: true,
  imports: [
    NzFormItemComponent,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzSpaceComponent,
    NzInputNumberComponent,
    NzSpaceItemDirective,
    FormsModule,
    ReactiveFormsModule,
    NzColDirective,
    NzRowDirective,
    TranslateModule,
    NgClass,
  ],
  templateUrl: './input-number.html',
  styleUrl: './input-number.less',
})
export class InputNumberComponent {
  readonly parentForm = input<FormGroup | any>(undefined);
  readonly controlName = input<string>(undefined);
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() label: string;
  readonly placeholder = input<string>(undefined);

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() prefix: string;
  readonly formatCurrency = input<boolean>(false);
  readonly formatDecimal = input<boolean>(false);
  readonly width = input<string>('auto');
  readonly step = input<number>(0.1);
  readonly precision = input<number>(2);
  readonly disabled = input<boolean>(false);
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() suffix: string;
  readonly minNumber = input<number>(undefined);
  readonly maxNumber = input<number>(undefined);
  readonly labelPosition = input<'left' | 'top'>('left');
  protected formatterCurrency = (value: number): string => {
    if (!!this.formatCurrency()) {
      return (
        value.toLocaleString('en-EN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }) + ' €'
      );
    } else if (!!this.formatDecimal()) {
      return value?.toLocaleString('en-EN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    } else {
      return value?.toString();
    }
  };

  isRequired() {
    return this.parentForm()
      .get(this.controlName())
      .hasValidator(Validators.required);
  }
}
