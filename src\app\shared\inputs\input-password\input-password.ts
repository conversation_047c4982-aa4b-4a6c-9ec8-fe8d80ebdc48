import { NgStyle } from '@angular/common';
import { Component, Input, input } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import {
  NzForm<PERSON>ontrolComponent,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzInputDirective, NzInputGroupComponent } from 'ng-zorro-antd/input';

@Component({
  selector: 'app-input-password',
  standalone: true,
  imports: [
    NzFormItemComponent,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzInputGroupComponent,
    NzInputDirective,
    NzIconDirective,
    FormsModule,
    TranslateModule,
    ReactiveFormsModule,
    NzRowDirective,
    NzColDirective,
    NgStyle,
  ],
  templateUrl: './input-password.html',
  styleUrl: './input-password.less',
})
export class InputPasswordComponent {
  readonly parentForm = input<FormGroup>(undefined);
  readonly controlName = input<string>(undefined);
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() label: string;
  readonly placeholder = input<string>('');
  readonly pattern = input<string>(undefined);
  readonly prefixIcon = input<string>(undefined);
  readonly minLength = input<number>(0);
  readonly maxLength = input<number>(999);
  readonly style = input<{
    [key: string]: any;
  }>(undefined);
  readonly labelPosition = input<'left' | 'top'>('left');
  protected passwordVisible = false;

  isRequired() {
    return this.parentForm()
      .get(this.controlName())
      .hasValidator(Validators.required);
  }

  getCombinedStyles(): { [key: string]: any } {
    return {
      ...this.style(),
      display: this.labelPosition() === 'left' ? 'flex' : 'block',
    };
  }
}
