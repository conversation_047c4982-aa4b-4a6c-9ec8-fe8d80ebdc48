@if (!isLoading()) {
  <div class="container">
    <a (click)="onBackToLoginClick()"
      ><span
        nz-icon
        nzType="client-ui:arrow-left"
        nzTheme="outline"
        class="mr-4"
      ></span
      >{{ "ACTIVATE.backToLogin" | translate }}</a
    >
    <h1 class="m-0">{{ "ACTIVATE.title" | translate }}</h1>
    <p class="mb-16">{{ "ACTIVATE.subtitle" | translate }}</p>
    <form
      nz-form
      [formGroup]="baseForm"
      class="reset-form"
      [nzLayout]="'vertical'"
    >
      <app-input-password
        [controlName]="'password'"
        [parentForm]="baseForm"
        [placeholder]="'********'"
        [prefixIcon]="'lock'"
        [label]="'ACTIVATE.password' | translate"
        [minLength]="9"
        [maxLength]="50"
      ></app-input-password>

      <app-input-password
        [controlName]="'confirmPassword'"
        [parentForm]="baseForm"
        [placeholder]="'********'"
        [prefixIcon]="'lock'"
        [label]="'ACTIVATE.confirmPassword' | translate"
        [minLength]="9"
        [maxLength]="50"
      ></app-input-password>
    </form>

    <app-simple-button
      [autoMinify]="false"
      [title]="'ACTIVATE.confirmButton' | translate | uppercase"
      [type]="'default'"
      (onButtonClick)="onActivateAccountClick()"
      [disabled]="!baseForm.valid || isLoading()"
      [style]="{ width: '100%' }"
    >
    </app-simple-button>
  </div>
}
