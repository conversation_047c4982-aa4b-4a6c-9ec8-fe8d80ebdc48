import { ITableRowAction } from './table.action';
import { ITableColumn } from './table.column';

export interface ITableSetting {
  /** Table columns definition. See ITableColumn iterface for more details.  */
  listOfColumns: ITableColumn[];
  /** Table pagination meta data */
  pagination: IPagination;
  /** Show row selection checkbox */
  showRowSelection?: boolean;
  /** If dynamicTableHeight, table height will be calculated as "window.innerHeight - offset" pixels. */
  dynamicTableHeightOffset: number;
  /** Actions available in context menu of a table row */
  singleRowActions?: ITableRowAction[];
  /** Actions available in multiple row selection context menu */
  multipleRowActions?: ITableRowAction[];
  /** Table Name for persistent Filter */
  tableName?: string;
  /** Table Perfistent Filter Flag*/
  areFiltersPersistent?: boolean;
  /** Table condition for enable o disable row selection */
  rowDisableSelectionFn?: (row: any) => boolean;
}

/** Define the Pagination object */
export interface IPagination {
  pageIndex: number;
  pageSize: number;
  total: number;
}
