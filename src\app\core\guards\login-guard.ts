import { inject } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  CanActivateFn,
  Router,
  RouterStateSnapshot,
} from "@angular/router";
import { GenericUtils } from "@core/utils/generic";

export const loginGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
) => {
  const router = inject(Router);
  const isLoggedIn: boolean = localStorage.getItem(GenericUtils.session_token)
    ? true
    : false;

  return isLoggedIn ? router.navigateByUrl("") : true;
};
