<div nz-row class="top-bar-desktop">
  <div nz-col nzSpan="24" class="top-bar-column">
    <!-- ***** Theme menu *****-->
    <nz-switch
      class="switch-top-bar"
      [ngModel]="currentTheme == themeType.dark"
      [nzCheckedChildren]="checkedTemplate"
      [nzUnCheckedChildren]="unCheckedTemplate"
      (ngModelChange)="
        changeTheme($event == true ? themeType.dark : themeType.light)
      "
      data-cy="theme-switch"
    >
    </nz-switch>
    <ng-template #unCheckedTemplate
      ><i nz-icon nzType="client-ui:light"></i
    ></ng-template>
    <ng-template #checkedTemplate
      ><i nz-icon nzType="client-ui:dark"></i
    ></ng-template>
    <!-- ***** Language menu *****-->
    @if (languages) {
      <a
        nz-dropdown
        [nzDropdownMenu]="menu_language"
        [nzPlacement]="'bottomCenter'"
        class="btn-top-bar btn-layout"
      >
        <i nz-icon nzType="client-ui:global"></i>
      </a>
    }

    <nz-dropdown-menu #menu_language="nzDropdownMenu">
      <ul nz-menu>
        @for (language of languages; track language.code) {
          @if (language.enabled) {
            <li
              [nzSelected]="language.code == currentLanguage"
              nz-menu-item
              (click)="changeLanguage(language)"
            >
              <i nz-icon nzType="client-ui:flag-{{ language.code }}"></i
              ><span class="ml-8">{{ language.name }}</span>
            </li>
          }
        }
      </ul>
    </nz-dropdown-menu>

    <!-- ***** User menu *****-->
    <a
      nz-dropdown
      [nzDropdownMenu]="menu_user"
      [nzPlacement]="'bottomRight'"
      class="btn-layout avatar-top-bar"
    >
      <nz-avatar
        [nzText]="userAvatarName() ? (userAvatarName() | uppercase) : null"
        [nzSize]="'small'"
        class="user-avatar"
      >
      </nz-avatar>
      <!-- <nz-avatar
        [nzIcon]="'client-ui:user'"
        [nzSize]="'small'"
        class="user-avatar"
      >
      </nz-avatar> -->
    </a>
    <nz-dropdown-menu #menu_user="nzDropdownMenu">
      <ul nz-menu>
        <!-- <ng-container>
          <li nz-menu-item (click)="onSettingsClick()">
            <i nz-icon nzType="control" nzTheme="outline"></i>
            <span class="ml-8">{{ 'TOP_BAR.preferences' | translate }}</span>
          </li>
          <li nz-menu-divider></li>
        </ng-container> -->

        <li nz-menu-item (click)="onProfileClick()">
          <i nz-icon nzType="client-ui:user" nzTheme="outline"></i>
          <span class="ml-8">{{ "profile" | translate }}</span>
        </li>

        <li nz-menu-item nzMenuClassName="logout">
          <i nz-icon nzType="client-ui:open-new" nzTheme="outline"></i>
          <span class="ml-8">{{ "TOP_BAR.website" | translate }}</span>
        </li>

        <li nz-menu-item (click)="onLogoutClick()" class="logout">
          <i nz-icon nzType="client-ui:logout" nzTheme="outline"></i>
          <span class="ml-8">
            {{ "TOP_BAR.logout" | translate }}
          </span>
        </li>
      </ul>
    </nz-dropdown-menu>
  </div>
</div>

<div nz-row class="top-bar-mobile">
  <div nz-col nzSpan="3" class="top-bar-column" (click)="onMobileDrawerOpen()">
    <div nz-icon nzType="client-ui:hamburger"></div>
  </div>

  <nz-drawer
    [nzClosable]="false"
    [nzVisible]="mobileDrawerVisible()"
    [nzPlacement]="'left'"
    [nzTitle]="tplDrawerTitle"
    (nzOnClose)="onMobileDrawerClose()"
  >
    <ng-container *nzDrawerContent>
      <ul nz-menu class="main-menu" nzTheme="light" [nzMode]="'inline'">
        <ng-container
          *ngTemplateOutlet="menuTpl; context: { menus: menus }"
        ></ng-container>
        <ng-template #menuTpl let-menus="menus">
          @for (menu of menus; track menu.id) {
            @if (menu.divider) {
              <nz-divider
                style="margin: 0"
                [nzText]="!isCollapsed ? (menu.divider.text | translate) : null"
                [nzOrientation]="'right'"
              />
            }

            @if (!menu.children) {
              <li
                nz-menu-item
                class="menu-item"
                [nzSelected]="menu.selected"
                [nzMatchRouterExact]="true"
                [nzDisabled]="menu.disabled"
                nz-tooltip
                [nzTooltipTitle]="
                  isCollapsed && menu.level == 0
                    ? (menu.title | translate)
                    : null
                "
                nzTooltipPlacement="right"
                (click)="!menu.disabled ? onMenuClick(menu) : null"
              >
                @if (menu.icon) {
                  <i nz-icon [nzType]="'client-ui:' + menu.icon"></i>
                }

                <span
                  [style]="
                    !isCollapsed && menu.level != 0 ? 'margin-left: 2.6rem' : ''
                  "
                  >{{ menu.title | translate }}</span
                >
              </li>
            } @else {
              <li
                class="menu-item"
                nz-submenu
                [nzOpen]="menu.open"
                [nzTitle]="menu.title | translate"
                [nzIcon]="menu.icon"
                [nzDisabled]="menu.disabled"
              >
                <ng-container
                  *ngTemplateOutlet="
                    menuTpl;
                    context: {
                      menus: menu.children,
                    }
                  "
                ></ng-container>
              </li>
            }
          }
        </ng-template>
      </ul>
    </ng-container>

    <ng-template #tplDrawerTitle>
      <div class="drawer-title">
        <span>Menu</span>
        <div
          nz-icon
          nzType="client-ui:close"
          (click)="onMobileDrawerClose()"
        ></div>
      </div>
    </ng-template>
  </nz-drawer>
  <div nz-col nzSpan="17" class="divider"></div>

  <div nz-col nzSpan="1" class="divider">
    <nz-divider [nzType]="'vertical'"></nz-divider>
  </div>
</div>

<!-- ALTERNATIVE TOP BAR MOBILE -->
<!-- <div nz-row class="top-bar-mobile">
  <div nz-col nzSpan="20" class="top-bar-column">
    <ul nz-menu nzMode="horizontal">
      @for (menu of menus; track menu.id) {
      
        <li
          nz-menu-item
          nzSelected
          [nzSelected]="menu.selected"
          [nzMatchRouterExact]="true"
          (click)="!menu.disabled && !menu.children ? onMenuClick(menu) : null"
        >
          <div nz-icon [nzType]="menu.icon"></div>
        </li>
      }
    </ul>
  </div>
  <div nz-col nzSpan="1" class="divider">
    <nz-divider [nzType]="'vertical'"></nz-divider>
  </div>

</div> -->
