import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NzRadioModule } from 'ng-zorro-antd/radio';

import { ToggleRadioComponent } from './toggle-radio';

describe('ToggleRadioComponent', () => {
  let component: ToggleRadioComponent;
  let fixture: ComponentFixture<ToggleRadioComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ToggleRadioComponent,
        FormsModule,
        TranslateModule.forRoot(),
        NzRadioModule,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ToggleRadioComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeDefined();
  });

  it('should emit onSelected when value changes', () => {
    const testOptions = [
      { label: 'Option 1', value: 'opt1' },
      { label: 'Option 2', value: 'opt2' },
    ];

    spyOn(component.onSelected, 'emit');

    fixture.componentRef.setInput('optionList', testOptions);
    component.value.set('opt1');

    expect(component.onSelected.emit).toHaveBeenCalledWith('opt1');
  });

  it('should display button options correctly', () => {
    const testOptions = [
      { label: 'Option 1', value: 'opt1' },
      { label: 'Option 2', value: 'opt2' },
    ];

    fixture.componentRef.setInput('optionList', testOptions);
    fixture.componentRef.setInput('label', 'Test Label');
    fixture.componentRef.setInput('radioType', 'button');
    fixture.detectChanges();

    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.radio-label')).toBeDefined();
    expect(compiled.querySelectorAll('label[nz-radio-button]').length).toBe(2);
  });

  it('should display circular radio options correctly', () => {
    const testOptions = [
      { label: 'Option 1', value: 'opt1' },
      { label: 'Option 2', value: 'opt2' },
    ];

    fixture.componentRef.setInput('optionList', testOptions);
    fixture.componentRef.setInput('label', 'Test Label');
    fixture.componentRef.setInput('radioType', 'radio');
    fixture.detectChanges();

    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.radio-label')).toBeDefined();
    expect(compiled.querySelectorAll('label[nz-radio]').length).toBe(2);
    expect(compiled.querySelector('.radio-group-circular')).toBeDefined();
    expect(compiled.querySelectorAll('.radio-text').length).toEqual(2);
  });
});
