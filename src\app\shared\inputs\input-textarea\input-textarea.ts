import { Component, Input, input } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  NzFormControlComponent,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzAutosizeDirective, NzInputDirective } from 'ng-zorro-antd/input';

@Component({
  selector: 'app-input-textarea',
  standalone: true,
  imports: [
    NzInputDirective,
    FormsModule,
    ReactiveFormsModule,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzAutosizeDirective,
    NzFormItemComponent,
    NzColDirective,
    NzRowDirective,
  ],
  templateUrl: './input-textarea.html',
  styleUrl: './input-textarea.less',
})
export class InputTextareaComponent {
  readonly parentForm = input<FormGroup | any>(undefined);
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() label: string;
  readonly controlName = input<string>(undefined);
  readonly placeholder = input<string>(undefined);
  readonly minLength = input<number>(0);
  readonly maxLength = input<number>(999);
  readonly size = input<{
    minRows: number;
    maxRows: number;
  }>(undefined);

  isRequired() {
    return this.parentForm()
      .get(this.controlName())
      .hasValidator(Validators.required);
  }
}
