import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '@core/services/http/auth';
import { ModalService } from '@core/services/utils/modal';
import { map } from 'rxjs';

export const registerGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);
  const modalService = inject(ModalService);

  return authService
    .isRegisterOpen()
    .pipe(
      map((res) =>
        res.data?.available ? true : router.createUrlTree(['/auth/login']),
      ),
    );
};
