<div class="header">
  <nz-page-header class="site-page-header" [nzSubtitle]="tplSubtitle">
    <!--avatar-->
    <nz-avatar
      nz-page-header-avatar
      nzSize="small"
      [nzIcon]="currentMenu()?.icon"
    ></nz-avatar>

    <!--title-->
    <nz-page-header-title>
      <span>
        <a (click)="gotoPage(currentMenu()?.routerLink)">{{
          currentMenu()?.title | translate
        }}</a></span
      >
    </nz-page-header-title>

    <!--subtitle-->
    <ng-template #tplSubtitle>
      @for (item of urls() | slice: 1; track $index) {
        @if (!breadcrumbIsLoading()) {
          <nz-page-header-subtitle>/</nz-page-header-subtitle>
          <nz-page-header-subtitle>
            <a
              class="breadcrumb"
              [ngClass]="{ disabled: $last }"
              (click)="!$last ? gotoSubpage(item) : null"
              >{{
                ($index === 0 && breadcrumbData() ? breadcrumbData() : item)
                  | translate
              }}</a
            >
          </nz-page-header-subtitle>
        }
      }
    </ng-template>

    <!--extra-->
    <nz-page-header-extra>
      <span>
        @switch (currentSection()) {
          @case (currentSectionType.dashboard) {
            <app-dashboard-header></app-dashboard-header>
          }
          @case (currentSectionType.categoryList) {
            <app-category-list-header></app-category-list-header>
          }
          @case (currentSectionType.categoryDetail) {
            <app-category-detail-header></app-category-detail-header>
          }
          @case (currentSectionType.adminList) {
            <app-admin-list-header></app-admin-list-header>
          }
          @case (currentSectionType.adminDetail) {
            <app-admin-detail-header></app-admin-detail-header>
          }
          @case (currentSectionType.staffList) {
            <app-staff-list-header></app-staff-list-header>
          }
          @case (currentSectionType.staffDetail) {
            <app-staff-detail-header></app-staff-detail-header>
          }
          @case (currentSectionType.productDetail) {
            <app-product-detail-header></app-product-detail-header>
          }
          @case (currentSectionType.productList) {
            <app-product-list-header></app-product-list-header>
          }

          @default {}
        }
      </span>
    </nz-page-header-extra>
  </nz-page-header>
  <nz-divider class="divider"></nz-divider>
</div>
