import { NgStyle } from '@angular/common';
import {
  Component,
  DestroyRef,
  effect,
  inject,
  input,
  model,
  OnInit,
  output,
  signal,
  TemplateRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzOptionComponent, NzSelectComponent } from 'ng-zorro-antd/select';
import { BehaviorSubject, debounceTime, filter, tap } from 'rxjs';

@Component({
  selector: 'app-toggle-select',
  standalone: true,
  imports: [
    FormsModule,
    NgStyle,
    NzSelectComponent,
    NzOptionComponent,
    TranslateModule,
    NzIconDirective,
  ],
  templateUrl: './toggle-select.html',
  styleUrl: './toggle-select.less',
})
export class ToggleSelectComponent implements OnInit {
  // SERVICES
  private destroyRef = inject(DestroyRef);

  // INPUTS
  label = input<string>();
  placeholder = input<string>();
  mode = input<'multiple' | 'tags' | 'default'>('default');
  size = input<'large' | 'small' | 'default'>('default');
  maxTagCount = input<number>(5);
  allowClear = input<boolean>(false);
  disabled = input<boolean>(false);
  showSearch = input<boolean>(false);
  serverSearch = input<boolean>(false);
  hideSelectedOptions = input<boolean>(false);
  optionList = input<{ label: string; value: any }[] | any>();
  configKey = input<{ label: string }>({
    label: 'label',
  });
  labelPosition = input<'left' | 'top'>('left');
  minLength = input<number>(0);
  notFound = input<string | TemplateRef<any>>();
  showDropdown = signal<{ [key: string]: string }>({ display: 'none' });

  protected searchChange$ = new BehaviorSubject('');
  isLoading = input<boolean>(false);
  value = model<any>(undefined);
  onSearch = output<string>();
  onSelected = output<any>();
  valueEffect = effect(() => {
    this.value();
    this.onSelected.emit(this.value());
  });
  constructor() {}

  ngOnInit(): void {
    this.searchChange$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap((value) => {
          if (value.length < this.minLength()) {
            this.showDropdown.set({ display: 'none' });
          }
        }),
        filter((value) => value.length >= this.minLength()),
        debounceTime(500),
      )
      .subscribe((data) => {
        this.showDropdown.set({});
        this.onSearch.emit(data);
      });
  }

  onSearchClick(value: any) {
    this.searchChange$.next(value);
  }
}
