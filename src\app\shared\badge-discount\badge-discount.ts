import { CurrencyPipe } from '@angular/common';
import { Component, input } from '@angular/core';
import { NzBadgeComponent } from 'ng-zorro-antd/badge';
import { NzIconDirective } from 'ng-zorro-antd/icon';

@Component({
  selector: 'app-badge-discount',
  standalone: true,
  imports: [NzBadgeComponent, NzIconDirective, CurrencyPipe],
  templateUrl: './badge-discount.html',
  styleUrl: './badge-discount.less',
})
export class BadgeDiscountComponent {
  readonly price = input<number>(undefined);
}
