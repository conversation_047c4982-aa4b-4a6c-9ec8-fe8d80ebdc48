import { NgStyle } from '@angular/common';
import { Component, effect, input, model, output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NzRadioComponent, NzRadioGroupComponent } from 'ng-zorro-antd/radio';

@Component({
  selector: 'app-toggle-radio',
  standalone: true,
  imports: [
    NgStyle,
    NzRadioGroupComponent,
    NzRadioComponent,
    FormsModule,
    TranslateModule,
  ],
  templateUrl: './toggle-radio.html',
  styleUrl: './toggle-radio.less',
})
export class ToggleRadioComponent {
  // Input properties
  label = input<string>();
  optionList = input<{ label: string; value: any }[] | any>([]);
  configKey = input<{ label: string; value: string }>({
    label: 'label',
    value: 'value',
  });
  buttonStyle = input<'outline' | 'solid'>('outline');
  radioType = input<'button' | 'radio'>('button');
  size = input<'large' | 'small' | 'default'>('default');
  disabled = input<boolean>(false);
  labelPosition = input<'left' | 'top'>('left');

  // Model for two-way binding
  value = model<any>(undefined);

  // Output events
  onSelected = output<any>();

  // Effect to emit selection changes
  valueEffect = effect(() => {
    const currentValue = this.value();
    this.onSelected.emit(currentValue);
  });
}
