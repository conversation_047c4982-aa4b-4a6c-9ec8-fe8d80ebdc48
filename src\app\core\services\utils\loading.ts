import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

/**
 * Servizio per la gestione dello stato di caricamento globale dell'applicazione.
 * Permette di tracciare loading multipli tramite url e aggiornare lo stato osservabile.
 */
@Injectable({
  providedIn: 'root',
})
export class LoadingService {
  /**
   * Observable che rappresenta lo stato di loading globale.
   */
  loading$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  #loadingMap: Map<string, boolean> = new Map<string, boolean>();

  /**
   * Imposta o rimuove lo stato di loading per una specifica url.
   * Aggiorna lo stato globale in base alle richieste attive.
   * @param loading true per attivare, false per disattivare
   * @param url identificativo della richiesta
   * @returns void
   */
  setLoading(loading: boolean, url: string): void {
    if (loading === true) {
      this.#loadingMap.set(url, loading);
      this.loading$.next(true);
    } else if (loading === false && this.#loadingMap.has(url)) {
      this.#loadingMap.delete(url);
    }
    if (this.#loadingMap.size === 0) {
      this.loading$.next(false);
    }
  }
}
