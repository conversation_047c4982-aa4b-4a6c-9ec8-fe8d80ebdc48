import { Injectable, signal } from '@angular/core';
import { crudActionType } from '@models/enums/crud-action-type';
import { ICrudPermission } from '@models/interfaces/permission';

/**
 * Servizio per la gestione dei permessi CRUD dell'utente sulle varie sezioni.
 * Permette di caricare, verificare e resettare i permessi.
 */
@Injectable({
  providedIn: 'root',
})
export class PermissionService {
  public permissions = signal<Map<string, ICrudPermission>>(new Map());

  /**
   * Carica i permessi a partire da una lista di permessi stringa.
   * @param permissions Array di permessi in formato stringa
   * @returns void
   */
  public loadPermissions(permissions: string[]): void {
    this.clearPermissions();

    if (!permissions || permissions.length <= 0) return;

    permissions.forEach((act) => {
      const section = act.split(':')[0];
      const crudType = act.split(':')[1];

      let crudPermission: ICrudPermission = this.permissions().has(section)
        ? this.permissions().get(section)
        : { read: false, create: false, update: false, delete: false };

      switch (crudType) {
        case 'read':
          crudPermission.read = true;
          break;
        case 'create':
          crudPermission.create = true;
          break;
        case 'update':
          crudPermission.update = true;
          break;
        case 'delete':
          crudPermission.delete = true;
          break;
      }

      this.permissions().set(section, crudPermission);
    });
  }

  /**
   * Restituisce i permessi CRUD per una determinata sezione.
   * @param permissionName Nome della sezione/permesso
   * @returns Oggetto ICrudPermission
   */
  public getPermission(permissionName: string): ICrudPermission {
    return this.permissions().get(permissionName);
  }

  /**
   * Verifica se l'utente ha il permesso di lettura per una sezione.
   * @param permissionName Nome della sezione/permesso
   * @returns true se ha permesso di lettura
   */
  public hasReadPermission(permissionName: string): boolean {
    let act = this.permissions().get(permissionName);
    if (act && act.read) return act.read;
    else return false;
  }

  /**
   * Verifica se l'utente ha il permesso di creazione per una sezione.
   * @param permissionName Nome della sezione/permesso
   * @returns true se ha permesso di creazione
   */
  public hasCreatePermission(permissionName: string): boolean {
    let act = this.permissions().get(permissionName);
    if (act && act.create) return act.create;
    else return false;
  }

  /**
   * Verifica se l'utente ha il permesso di aggiornamento per una sezione.
   * @param permissionName Nome della sezione/permesso
   * @returns true se ha permesso di aggiornamento
   */
  public hasUpdatePermission(permissionName: string): boolean {
    let act = this.permissions().get(permissionName);
    if (act && act.update) return act.update;
    else return false;
  }

  /**
   * Verifica se l'utente ha il permesso di eliminazione per una sezione.
   * @param permissionName Nome della sezione/permesso
   * @returns true se ha permesso di eliminazione
   */
  public hasDeletePermission(permissionName: string): boolean {
    let act = this.permissions().get(permissionName);
    if (act && act.delete) return act.delete;
    else return false;
  }

  /**
   * Verifica se l'utente ha un determinato permesso CRUD su una sezione.
   * @param section Nome della sezione
   * @param crudType Tipo di permesso CRUD
   * @returns true/false/null a seconda del permesso richiesto
   */
  public searchPermission(section: string, crudType: crudActionType) {
    switch (crudType) {
      case crudActionType.create:
        return this.hasCreatePermission(section);
      case crudActionType.read:
        return this.hasReadPermission(section);
      case crudActionType.update:
        return this.hasUpdatePermission(section);
      case crudActionType.delete:
        return this.hasDeletePermission(section);

      default:
        return null;
    }
  }

  private clearPermissions() {
    this.permissions().clear();
  }
}
