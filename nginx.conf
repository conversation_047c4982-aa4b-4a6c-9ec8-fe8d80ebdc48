server {
    listen 80;
    server_name bellezza-boutique.memmcode.ddns.net; # Removed "http://" from the server_name

    root /usr/share/nginx/html;
    index index.html;

    # Serve static files directly and disable caching
    location / {
        # Security headers
        add_header X-Content-Type-Options nosniff;
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-XSS-Protection "1; mode=block";

        # Disable caching for the main content
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Pragma "no-cache";
        add_header Expires "0";

        try_files $uri /index.html;
    }

    # Static file handling with caching disabled (optional for testing or SPA)
    location ~* \.(?:ico|css|js|gif|jpg|jpeg|png|woff|woff2|ttf|svg)$ {
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        add_header Pragma "no-cache";
        add_header Expires "0";
        access_log off; # Reduce logging for static files
    }

    # Error customization: Redirect 404 errors to index.html (useful for SPAs)
    error_page 404 /index.html;

    # If you want to enable a standalone 404 page for non-SPA setups:
    # error_page 404 /404.html;
    # location = /404.html {
    #     internal;
    # }
}
