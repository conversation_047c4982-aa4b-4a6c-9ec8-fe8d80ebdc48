<div class="active-filters">
  <!-- ***** NO ACTIVE FILTERS *****-->
  @if (filters.length == 0) {
    <i nz-icon nzType="tags" nzTheme="outline"></i>
    <span class="no-filter" style="margin-left: 8px">{{
      'TABLE.noActiveFilter' | translate
    }}</span>
  }
  <!-- ***** ACTIVE FILTERS LIST *****-->
  @if (filters.length > 0) {
    <span class="pad-r-8">
      <span
        nzTooltipTitle="{{ 'TABLE.activeFilters' | translate }}"
        nzTooltipPlacement="right"
        nz-tooltip
        nzType="link"
        >
        <i nz-icon nzType="tags" nzTheme="outline"></i>
      </span>
    </span>
    <!-- ***** FIRST 3 ACTIVE FILTERS *****-->
    @for (filter of filters; track filter; let fi = $index) {
      @if (fi < maxActiveFilterTagCount) {
        <ng-template
          [ngTemplateOutletContext]="{ filter: filter }"
          [ngTemplateOutlet]="tplFilterTag"
          >
        </ng-template>
      }
    }
    <!-- ***** VIEW OTHER ACTIVE FILTERS *****-->
    @if (filters.length > maxActiveFilterTagCount) {
      <nz-tag
        class="filter-more-tag"
        style="margin-right: 4px"
        >
        <a
          nz-popover
          [nzPopoverTitle]="filterPopoverTitleTemplate"
          [(nzPopoverVisible)]="filterPopoverVisible"
          [nzPopoverPlacement]="'bottom'"
          nzPopoverTrigger="click"
          [nzPopoverContent]="filterPopoverContentTemplate"
          >
          @if (filterPopoverVisible) {
            {{ 'TABLE.seeLess' | translate }}
          }
          @if (!filterPopoverVisible) {
            {{ 'TABLE.seeMore' | translate
            }}{{ filters.length - maxActiveFilterTagCount }}
          }
        </a>
        <ng-template #filterPopoverTitleTemplate>
          <div style="font-size: 13px; font-weight: 600">
            <div nz-row style="width: 100%">
              <div nz-col nzSpan="23" style="text-transform: uppercase">
                {{ 'TABLE.activeFilters' | translate }}
              </div>
              <div nz-col nzSpan="1">
                <i
                  nz-icon
                  nzType="close"
                  nzTheme="outline"
                  style="cursor: pointer"
                  (click)="filterPopoverVisible = false"
                ></i>
              </div>
            </div>
          </div>
        </ng-template>
        <ng-template #filterPopoverContentTemplate>
          <div style="padding: 4px; width: 320px">
            @for (filter of filters; track filter) {
              <ng-template
                [ngTemplateOutletContext]="{ filter: filter, mgb: '4px' }"
                [ngTemplateOutlet]="tplFilterTag"
                >
              </ng-template>
            }
          </div>
        </ng-template>
      </nz-tag>
    }
    <!-- ***** CLOSE ALL FILTERS *****-->
    @if (filters.length > 1 && closeAllVisible) {
      <button
        nz-button
        nzType="primary"
        [nzSize]="'small'"
        class="margin-l-4"
        nz-tooltip
        [nzTooltipTitle]="'TABLE.closeActiveFilters' | translate"
        nzTooltipPlacement="top"
        (click)="closeAllFilters()"
        >
        <i nz-icon nzType="close"></i>
      </button>
    }
  }
</div>

<ng-template #tplFilterTag let-filter="filter" let-mgb="mgb">
  <nz-tag
    [nzMode]="getTagMode(filter)"
    (nzOnClose)="closeFilter(filter)"
    [ngStyle]="{ 'margin-right': '4px', 'margin-bottom': mgb }"
    nz-tooltip
    [nzTooltipTitle]="tplTipTitle"
    nzTooltipPlacement="top"
    >
    {{ getFilterTitleByKey(filter.key) | translate }}
  </nz-tag>
  <ng-template #tplTipTitle>
    <span [innerHTML]="displayFilterValue(filter) | translate"></span>
  </ng-template>
</ng-template>
