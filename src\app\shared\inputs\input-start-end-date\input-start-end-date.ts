import { Component, Input, input, viewChild } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NzDatePickerComponent } from 'ng-zorro-antd/date-picker';
import {
  NzFormControlComponent,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';

@Component({
  selector: 'app-input-start-end-date',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NzFormItemComponent,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzDatePickerComponent,
  ],
  templateUrl: './input-start-end-date.html',
  styleUrl: './input-start-end-date.less',
})
export class InputSartEndDateComponent {
  readonly parentForm = input<FormGroup | any>(undefined);
  readonly controlNameStart = input<string>(undefined);
  readonly controlNameEnd = input<string>(undefined);
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() labelStart: string;
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() labelEnd: string;
  readonly placeholderStart = input<string>(undefined);
  readonly placeholderEnd = input<string>(undefined);

  protected startValue: Date | null = null;
  protected endValue: Date | null = null;
  readonly endDatePicker = viewChild.required<NzDatePickerComponent>('endDatePicker');

  disabledStartDate = (startValue: Date): boolean => {
    if (!startValue || !this.endValue) {
      return false;
    }
    return startValue.getTime() > this.endValue.getTime();
  };

  disabledEndDate = (endValue: Date): boolean => {
    if (!endValue || !this.startValue) {
      return false;
    }
    return endValue <= this.startValue;
  };

  isStartRequired() {
    return this.parentForm()
      .get(this.controlNameStart())
      .hasValidator(Validators.required);
  }

  isEndRequired() {
    return this.parentForm()
      .get(this.controlNameEnd())
      .hasValidator(Validators.required);
  }

  handleStartOpenChange(open: boolean): void {
    if (!open) {
      this.endDatePicker().open();
    }
    // log('handleStartOpenChange', open);
  }

  handleEndOpenChange(open: boolean): void {
    // log('handleEndOpenChange', open);
  }
}
