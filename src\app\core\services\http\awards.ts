import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
import { IAward } from '@models/interfaces/award';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { CrudApiOperations } from '@models/interfaces/crud-api';
import { IRequestFilter, IRequestMeta } from '@shared/table/types/table.query';
import { Observable, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AwardsService implements CrudApiOperations<IAward, string> {
  // SERVICES
  private http = inject(HttpClient);

  // VARIABLES
  private _baseAwardsApi = `${environment.api.awards}`;

  private _award = signal<IAward | undefined>(undefined);
  readonly award$ = this._award.asReadonly();

  public setAward(award: IAward | undefined): void {
    this._award.set(award);
  }

  create(award: IAward) {
    return this.http.post<IBaseResponse<IAward>>(
      `${this._baseAwardsApi}`,
      award,
    );
  }

  update(awardId: string, award: IAward) {
    return this.http.put<IBaseResponse<IAward>>(
      `${this._baseAwardsApi}/${awardId}`,
      award,
    );
  }

  delete(awardId: string) {
    return this.http.delete<void>(`${this._baseAwardsApi}/${awardId}`);
  }

  readOne(awardId: string) {
    return this.http
      .get<IBaseResponse<IAward>>(`${this._baseAwardsApi}/${awardId}`)
      .pipe(tap((value) => this.setAward(value.data!)));
  }

  search(
    meta: IRequestMeta,
    filter: IRequestFilter[],
  ): Observable<IBaseResponse<IAward[], IFindResponseMeta>> {
    return this.http.post<IBaseResponse<IAward[], IFindResponseMeta>>(
      `${this._baseAwardsApi}/search`,
      {
        meta,
        filter,
      },
    );
  }
}
