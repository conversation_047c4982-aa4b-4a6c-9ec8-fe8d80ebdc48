const gulp = require('gulp');
const purgecss = require('gulp-purgecss');
const { series, src, dest } = require('gulp');
const clean = require('gulp-clean');

/**
 * Copy all files from the `assets` folder to the `dist/wws-ux-ui/src/lib/assets` folder
 * @returns Nothing.
 */
function browserFolderToPublicRoot() {
  return src(['./public/browser/**/*']).pipe(dest('./public/'));
}

function delBrowserFolder() {
  return src(['./public/browser']).pipe(clean({ force: true }));
}

function copySitemap() {
  return src(['./sitemap.xml']).pipe(dest('./public/'));
}
//
// function copyRobots() {
//   return src(['./Robots.txt']).pipe(dest('./public/'));
// }
//
function copyRedirects() {
  return src(['./_redirects']).pipe(dest('./public/'));
}

exports.default = series(
  // browserFolderToPublicRoot,
  // delBrowserFolder,
  // copyRedirects,
  copySitemap,
  // copyRobots,
);
