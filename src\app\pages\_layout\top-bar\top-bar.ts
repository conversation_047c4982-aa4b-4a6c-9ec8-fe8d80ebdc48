import { NgTemplateOutlet, UpperCasePipe } from '@angular/common';
import {
  Component,
  computed,
  DestroyRef,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '@core/services/http/auth';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  ILanguage,
  languageCodeType,
  LanguageService,
} from '@core/services/utils/language';
import { ThemeService } from '@core/services/utils/theme';
import { log } from '@core/utils/logger';
import { environment } from '@env/environment';
import { themeType } from '@models/enums/theme';
import { ISection } from '@models/interfaces/menu';
import { TranslateModule } from '@ngx-translate/core';
import { NzAvatarComponent } from 'ng-zorro-antd/avatar';
import { NzDividerComponent } from 'ng-zorro-antd/divider';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import {
  NzDropDownDirective,
  NzDropdownMenuComponent,
} from 'ng-zorro-antd/dropdown';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzListModule } from 'ng-zorro-antd/list';
import {
  NzMenuDirective,
  NzMenuItemComponent,
  NzMenuModule,
} from 'ng-zorro-antd/menu';
import { NzSwitchComponent } from 'ng-zorro-antd/switch';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';

@Component({
  selector: 'app-top-bar',
  standalone: true,
  imports: [
    NzDropDownDirective,
    NzDropdownMenuComponent,
    TranslateModule,
    NzAvatarComponent,
    NzMenuItemComponent,
    NzMenuDirective,
    NzSwitchComponent,
    FormsModule,
    TranslateModule,
    NzIconDirective,
    NzMenuModule,
    NzGridModule,
    NzDividerComponent,
    NzDrawerModule,
    NzTooltipDirective,
    NgTemplateOutlet,
    NzListModule,
    UpperCasePipe,
  ],
  templateUrl: './top-bar.html',
  styleUrl: './top-bar.less',
})
export class TopBarComponent implements OnInit {
  // SERVICES
  private themeService = inject(ThemeService);
  private languageService = inject(LanguageService);
  private authService = inject(AuthService);
  private router = inject(Router);
  private destroyRef = inject(DestroyRef);

  // VARIABLES
  public currentLanguage: languageCodeType;
  public currentTheme: themeType;
  public languages: ILanguage[] = environment.languages;
  public themeType = themeType;
  readonly userAvatarName = computed(
    () =>
      this.authService.user()?.name?.slice(0, 1)! +
      this.authService.user()?.surname?.slice(0, 1),
  );

  isOwner = computed(() => this.authService.user()?.owner);

  protected menus: ISection[] = environment.sections;
  protected mobileDrawerVisible = signal<boolean>(false);
  protected isCollapsed = false;

  constructor() {
    this.currentTheme = this.themeService.currentTheme;
    // Inizializza currentLanguage solo se il servizio è già stato inizializzato
    this.currentLanguage =
      this.languageService.currentLanguage?.code || languageCodeType.it;
  }

  ngOnInit(): void {
    this.themeService.theme$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((theme) => {
        this.currentTheme = theme;
      });

    this.languageService.language$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((lang) => (this.currentLanguage = lang));
  }

  changeLanguage(language: ILanguage) {
    log('LANGUAGE SELECTED: ', language);
    this.languageService.setLanguage(language);
  }

  onMenuClick(menu: ISection) {
    this.router.navigate([menu.routerLink]);
    this.mobileDrawerVisible.set(false);
  }

  onLogoutClick() {
    this.authService.logout().subscribe();
  }

  changeTheme(theme: themeType) {
    this.themeService.toggleTheme(theme);
  }

  onMobileDrawerOpen() {
    this.mobileDrawerVisible.set(true);
  }

  onMobileDrawerClose() {
    this.mobileDrawerVisible.set(false);
  }

  onProfileClick() {
    this.router.navigate(['/profile/info']);
  }
}
