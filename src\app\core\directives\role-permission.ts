import {
  afterRenderEffect,
  Directive,
  inject,
  input,
  TemplateRef,
  untracked,
  ViewContainerRef,
} from '@angular/core';
import { AuthService } from '@core/services/http/auth';
import { roleType } from '@models/enums/role';

@Directive({
  selector: '[rolePermission]',
  standalone: true,
})
export class RolePermissionDirective {
  // SERVICES
  private authService = inject(AuthService);
  private templateRef = inject(TemplateRef<any>);
  private viewContainer = inject(ViewContainerRef);

  // INPUTS
  rolePermission = input.required<roleType[]>();

  // PROPERTIES
  private user = inject(AuthService).user;

  constructor() {
    afterRenderEffect(() => {
      // Executed only if user is defined and the DOM is rendered
      if (this.user()) {
        untracked(() => this.checkStaffPermission());
      }
    });
  }

  checkStaffPermission() {
    const userRole = this.authService.user()?.role;
    console.log('USER ROLE', userRole);
    if (this.rolePermission().includes(userRole)) {
      this.viewContainer.createEmbeddedView(this.templateRef);
    } else {
      this.viewContainer.clear();
    }
  }
}
