import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { RolePermissionDirective } from '@core/directives/role-permission';
import { roleType } from '@models/enums/role';
import { TranslateModule } from '@ngx-translate/core';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-admin-list-header',
  standalone: true,
  imports: [
    NzSpaceComponent,
    NzSpaceItemDirective,
    SimpleButtonComponent,
    TranslateModule,
    PopoverButtonComponent,
    RolePermissionDirective,
  ],
  templateUrl: './admin-list-header.html',
  styleUrl: './admin-list-header.less',
})
export class AdminListHeader {
  // SERVICES
  private router = inject(Router);

  // ENUMS
  roleType = roleType;

  onCreateAdminClick() {
    this.router.navigateByUrl('/admin/create');
  }
}
