import { Component, inject, signal } from '@angular/core';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { init, trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/http/auth';
import { PageComponent } from '@pages/_layout/page/page';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { IconService } from '@core/services/utils/icon';
import { MessageService } from '@core/services/utils/message';
import packageJson from '../../package.json';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, PageComponent],
  templateUrl: './app.html',
  styleUrl: './app.less',
})
export class App {
  // SERVICES
  private authService = inject(AuthService);
  private router = inject(Router);
  private messageService = inject(MessageService);

  // PROPERTIES
  protected standaloneUrl = '/auth';
  protected singlePage = signal<boolean>(true);

  // COMPUTED
  readonly isLoggedIn = this.authService.isLoggedIn;
  protected isLoading = this.messageService.isLoading;

  // Inject icon service - Do not remove!
  protected iconService = inject(IconService);

  constructor() {
    init(process.env['APTABASE_API_KEY'], {
      appVersion: packageJson.version,
      host: process.env['APTABASE_HOST'],
      isDebug: process.env['APTABASE_IS_DEBUG'] === 'true',
    });

    trackEvent('app_loaded');
    this.router.events.pipe(takeUntilDestroyed()).subscribe((event) => {
      if (event instanceof NavigationEnd) {
        const currentUrl = event.urlAfterRedirects;
        this.singlePage.set(currentUrl.includes(this.standaloneUrl));
      }
    });
  }
}
