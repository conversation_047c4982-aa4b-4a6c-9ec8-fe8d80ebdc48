import { Routes } from '@angular/router';
import { crudActionType } from '@models/enums/crud-action-type';
import { ProductListComponent } from '@pages/products/product-list/product-list';
import { ProductCreateUpdateComponent } from './product-create-update/product-create-update';

export const PRODUCTS_ROUTES: Routes = [
  {
    path: '',
    component: ProductListComponent,
  },
  {
    path: 'create',
    component: ProductCreateUpdateComponent,
    data: { crudType: crudActionType.create },
  },
  {
    path: ':id',
    component: ProductCreateUpdateComponent,
    data: { crudType: crudActionType.update },
  },
];
