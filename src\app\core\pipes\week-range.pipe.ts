import { Pipe, PipeTransform } from '@angular/core';
import { formatDate } from "@angular/common";

@Pipe({
  name: 'weekRange',
  standalone: true
})
export class WeekRangePipe implements PipeTransform {

  transform(date: Date): string {
    const locale = 'it-IT';

    // Calculate the first and last day of the week (assuming the week starts on Monday)
    const day = date.getDay(); // 0 (Sunday) to 6 (Saturday)
    const diffToMonday = day === 0 ? -6 : 1 - day; // Adjust for Sunday
    const startDate = new Date(date);
    startDate.setDate(date.getDate() + diffToMonday);

    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);

    const start = formatDate(startDate, 'd MMMM', locale); // e.g., 5 Gennaio
    const end = formatDate(endDate, 'd MMMM yyyy', locale); // e.g., 11 Gennaio 2025

    return `${start} - ${end}`;
  }
}

