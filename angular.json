{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "cache": {"enabled": false}}, "newProjectRoot": "projects", "projects": {"client": {"architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "configurations": {"dev": {"extractLicenses": false, "namedChunks": true, "optimization": false, "sourceMap": true}, "local": {"extractLicenses": false, "namedChunks": true, "optimization": false, "sourceMap": true}, "mock": {"extractLicenses": false, "namedChunks": true, "optimization": false, "sourceMap": true}, "prod": {"budgets": [{"maximumError": "5mb", "maximumWarning": "500kb", "type": "initial"}, {"maximumError": "5mb", "maximumWarning": "2kb", "type": "anyComponentStyle"}], "extractLicenses": true, "namedChunks": false, "optimization": true, "outputHashing": "all", "sourceMap": false}, "test": {"extractLicenses": false, "optimization": true, "sourceMap": true}}, "defaultConfiguration": "prod", "options": {"allowedCommonJsDependencies": ["lodash"], "aot": true, "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, "src/favicon.ico", {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "customWebpackConfig": {"path": "./webpack.config.js"}, "index": "src/index.html", "inlineStyleLanguage": "less", "main": "src/main.ts", "outputPath": "public", "stylePreprocessorOptions": {"includePaths": ["src/styles/themes"]}, "styles": ["src/styles.less", {"bundleName": "light", "inject": false, "input": "src/styles/light.less"}, {"bundleName": "dark", "inject": false, "input": "src/styles/dark.less"}], "tsConfig": "tsconfig.app.json"}}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "configurations": {"dev": {"buildTarget": "client:build:dev", "proxyConfig": "src/environments/proxy.conf.js"}, "local": {"buildTarget": "client:build:local", "proxyConfig": "src/environments/proxy.conf.js"}, "mock": {"buildTarget": "client:build:mock", "proxyConfig": "src/environments/proxy.conf.js"}}, "defaultConfiguration": "dev"}, "test": {"builder": "@angular/build:karma", "options": {"assets": [{"glob": "**/*", "input": "public"}], "inlineStyleLanguage": "scss", "styles": ["src/styles.less"], "tsConfig": "tsconfig.spec.json"}}}, "prefix": "app", "projectType": "application", "root": "", "schematics": {"@schematics/angular:component": {"style": "less"}}, "sourceRoot": "src"}}, "version": 1}