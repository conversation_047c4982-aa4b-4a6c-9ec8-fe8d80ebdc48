import { Component, input, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';

@Component({
  selector: 'app-stripe-button',
  standalone: true,
  imports: [NzButtonModule, NzIconModule, TranslateModule],
  templateUrl: './stripe-button.html',
  styleUrl: './stripe-button.less',
})
export class StripeButtonComponent {
  readonly style = input<{
    [key: string]: any;
  }>(undefined);
  readonly variant = input<'pay' | 'manage'>('pay');
  readonly text = input<string>('SUBSCRIPTION.payWith');
  readonly onClick = output<void>();

  handleClick() {
    // TODO: The 'emit' function requires a mandatory void argument
    this.onClick.emit();
  }
}
