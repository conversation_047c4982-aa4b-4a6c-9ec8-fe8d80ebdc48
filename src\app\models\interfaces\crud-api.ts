import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { IRequestFilter, IRequestMeta } from '@shared/table/types/table.query';
import type { Observable } from 'rxjs';

/** Define the Crud API Operation */
export interface CrudApiOperations<T, ID> {
  create?(t: T): Observable<IBaseResponse<T>>;
  readOne?(id: ID): Observable<IBaseResponse<T>>;
  readAll?(): Observable<IBaseResponse<T[]>>;
  update?(id: ID, t: T): Observable<IBaseResponse<T>>;
  delete?(id: ID): Observable<any>;
  search?(
    meta: IRequestMeta,
    filter: IRequestFilter[],
  ): Observable<IBaseResponse<T[], IFindResponseMeta>>;
}
