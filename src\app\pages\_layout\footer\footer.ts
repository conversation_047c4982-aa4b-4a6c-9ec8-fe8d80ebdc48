import { Component } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import packageJson from '../../../../../package.json';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [TranslateModule],
  templateUrl: './footer.html',
  styleUrl: './footer.less',
})
export class FooterComponent {
  protected copyright: string =
    'Copyright © ' + new Date(Date.now()).getFullYear().toString() + ' - ';
  protected version: string = packageJson.version;
}
