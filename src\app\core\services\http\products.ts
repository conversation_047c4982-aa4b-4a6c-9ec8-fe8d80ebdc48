import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { CrudApiOperations } from '@models/interfaces/crud-api';
import { IProduct } from '@models/interfaces/product';
import { IRequestFilter, IRequestMeta } from '@shared/table/types/table.query';
import { Observable, tap } from 'rxjs';

/**
 * Servizio per la gestione dei prodotti.
 * Fornisce CRUD e stato locale per i prodotti tramite API.
 */
@Injectable({
  providedIn: 'root',
})
export class ProductsService implements CrudApiOperations<IProduct, string> {
  // SERVICES
  private http = inject(HttpClient);

  private _product = signal<IProduct | undefined>(undefined);
  readonly product$ = this._product.asReadonly();

  // VARIABLES
  private _baseProductsApi = `${environment.api.products}`;

  /**
   * Imposta il prodotto attivo nello stato locale.
   * @param product Prodotto da impostare come attivo
   * @returns void
   */
  public setProduct(product: IProduct | undefined): void {
    this._product.set(product);
  }

  /**
   * Crea un nuovo prodotto tramite API.
   * @param product Oggetto prodotto da creare
   * @returns Observable con la risposta della creazione
   */
  create(product: IProduct) {
    return this.http.post<IBaseResponse<IProduct>>(
      `${this._baseProductsApi}`,
      product,
    );
  }

  /**
   * Aggiorna un prodotto esistente tramite API.
   * @param productId ID del prodotto da aggiornare
   * @param product Nuovi dati del prodotto
   * @returns Observable con la risposta dell'aggiornamento
   */
  update(productId: string, product: IProduct) {
    return this.http
      .put<
        IBaseResponse<IProduct>
      >(`${this._baseProductsApi}/${productId}`, product)
      .pipe(
        tap((value) => {
          this.setProduct(value.data);
        }),
      );
  }

  /**
   * Elimina un prodotto tramite API.
   * @param productId ID del prodotto da eliminare
   * @returns Observable con la risposta dell'eliminazione
   */
  delete(productId: string) {
    return this.http.delete<void>(`${this._baseProductsApi}/${productId}`);
  }

  /**
   * Recupera un singolo prodotto tramite API e aggiorna lo stato locale.
   * @param productId ID del prodotto da recuperare
   * @returns Observable con il prodotto trovato
   */
  readOne(productId: string) {
    return this.http
      .get<IBaseResponse<IProduct>>(`${this._baseProductsApi}/${productId}`)
      .pipe(
        tap((value) => {
          this.setProduct(value.data);
        }),
      );
  }

  /**
   * Recupera prodotti tramite ricerca per nome.
   * @param productName Nome del prodotto da cercare
   * @returns Observable con la lista dei prodotti trovati
   */
  readProductsByName(
    productName: string,
  ): Observable<IBaseResponse<IProduct[]>> {
    return this.http.get<IBaseResponse<IProduct[]>>(
      `${this._baseProductsApi}/search?name=${productName}`,
    );
  }

  search(
    meta: IRequestMeta,
    filter: IRequestFilter[],
  ): Observable<IBaseResponse<IProduct[], IFindResponseMeta>> {
    return this.http.post<IBaseResponse<IProduct[], IFindResponseMeta>>(
      `${this._baseProductsApi}/search`,
      {
        meta,
        filter,
      },
    );
  }
}
