import { NgTemplateOutlet } from '@angular/common';
import { Component, inject } from '@angular/core';
import { RolePermissionDirective } from '@core/directives/role-permission';
import { HeaderService } from '@core/services/utils/header';
import { roleType } from '@models/enums/role';
import { TranslateModule } from '@ngx-translate/core';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { NzSpaceModule } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-category-detail-header',
  imports: [
    NgTemplateOutlet,
    PopoverButtonComponent,
    TranslateModule,
    NzSpaceModule,
    RolePermissionDirective,
    NgTemplateOutlet,
  ],
  templateUrl: './category-detail-header.html',
  styleUrl: './category-detail-header.less',
})
export class CategoryDetailHeader {
  // SERVICES
  private headerService = inject(HeaderService);
  // PROPERTIES

  protected tplMainButton = this.headerService.template;

  // ENUMS
  roleType = roleType;
}
