import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-product-list-header',
  imports: [
    NzSpaceComponent,
    SimpleButtonComponent,
    NzSpaceItemDirective,
    PopoverButtonComponent,
    TranslateModule,
  ],
  templateUrl: './product-list-header.html',
  styleUrl: './product-list-header.less',
})
export class ProductListHeader {
  private router = inject(Router);

  onAddProductClick() {
    this.router.navigateByUrl('/products/create');
  }
}
