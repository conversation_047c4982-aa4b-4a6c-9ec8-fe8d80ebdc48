import { Routes } from '@angular/router';

import { crudActionType } from '@models/enums/crud-action-type';
import { CategoryCreateUpdateComponent } from './category-create-update/category-create-update';
import { CategoryListComponent } from './category-list/category-list';

export const CATEGORIES_ROUTES: Routes = [
  { path: '', component: CategoryListComponent },
  {
    path: 'create',
    component: CategoryCreateUpdateComponent,
    data: { crudType: crudActionType.create },
  },
  {
    path: ':id',
    component: CategoryCreateUpdateComponent,
    data: { crudType: crudActionType.update },
  },
];
