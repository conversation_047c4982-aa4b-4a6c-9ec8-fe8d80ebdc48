import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { ITableFilterItem } from './types/table.filter';

@Injectable()
export class TableConfigService {
  constructor() {}

  // Active Filter Object
  public readonly activeFilter$ = new BehaviorSubject<ITableFilterItem[]>([]);
  //Column Filter (used for update table column filter)
  public readonly tableColumFilter$ = new Subject<string>();
  //Close all filter (used for notify when closeAllFilter is clicked)
  public readonly closeAllColumnFilters$ = new Subject<void>();
  //Update column event
  public readonly updateColumn$ = new Subject<void>();
}
