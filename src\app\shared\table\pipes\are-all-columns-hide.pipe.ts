import { Pipe, PipeTransform } from '@angular/core';
import { ITableColumn } from '../types/table.column';

@Pipe({
  name: 'areAllColumnsHide',
  pure: false,
  standalone: true
})
export class AreAllColumnsHidePipe implements PipeTransform {
  transform(listOfColumns: ITableColumn[]): boolean {
    return (
      listOfColumns.length / 2 <=
      listOfColumns.filter(col => !col.visible).length
    );
  }
}
