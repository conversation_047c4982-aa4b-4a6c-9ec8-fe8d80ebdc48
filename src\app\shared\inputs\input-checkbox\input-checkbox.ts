import { NgStyle } from '@angular/common';
import { Component, EventEmitter, Input, input } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NzCheckboxComponent } from 'ng-zorro-antd/checkbox';
import {
  NzFormControlComponent,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';

@Component({
  selector: 'app-input-checkbox',
  standalone: true,
  imports: [
    NgStyle,
    NzCheckboxComponent,
    FormsModule,
    ReactiveFormsModule,
    NzFormItemComponent,
    NzFormControlComponent,
    NzFormLabelComponent,
    NzColDirective,
    NzRowDirective,
    TranslateModule,
  ],
  templateUrl: './input-checkbox.html',
  styleUrl: './input-checkbox.less',
})
export class InputCheckboxComponent {
  readonly parentForm = input<FormGroup>(undefined);
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() label: string;
  readonly controlName = input<string>(undefined);
  readonly name = input<string>(undefined);
  readonly optionList = input<
    {
      label: string;
      value: any;
    }[]
  >([]);
  readonly labelPosition = input<'left' | 'top'>('left');
  readonly style = input<{
    [key: string]: string;
  }>(undefined);
  protected onCheckboxChange: EventEmitter<Event> = new EventEmitter<Event>();

  isRequired() {
    return this.parentForm()
      .get(this.controlName())
      .hasValidator(Validators.required);
  }
}
