import { Injectable, signal } from '@angular/core';
import { ISection } from '@models/interfaces/menu';

/**
 * Servizio per la gestione dello stato del layout e del menu corrente.
 */
@Injectable({
  providedIn: 'root',
})
export class LayoutService {
  /**
   * Stato reattivo che rappresenta la sezione/menu corrente.
   */
  public currentMenu$ = signal<ISection | undefined>(undefined);

  /**
   * Crea una nuova istanza del servizio LayoutService.
   */
  constructor() {}
}
