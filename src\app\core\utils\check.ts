import { TemplateRef } from '@angular/core';
import { isEmpty, isEqual } from 'lodash';

/**Check Type/Value Utils */
export class CheckUtils {
  /**
   * Check if a value is null, undefined or empty
   * @param value
   * @returns true if value is null, undefined or empty, otherwise false
   */
  public static isNullUndefinedOrEmpty(value: any): boolean {
    return (
      this.isNil(value) ||
      this.isEmptyString(value) ||
      (this.isArray(value) && value.length == 0) ||
      (typeof value == 'object' &&
        Object.keys(value).length === 0 &&
        !this.isDateObj(value))
    );
  }

  /**
   * Check if value is not null or undefined
   * @param value
   * @returns value is NonNullable<T>
   */
  public static isNotNil<T>(value: T): value is NonNullable<T> {
    return typeof value !== 'undefined' && value !== null;
  }

  /**
   * Check if value is null or undefined
   * @param value
   * @returns value is null | undefined
   */
  public static isNil<T>(value: T): boolean {
    return typeof value === 'undefined' || value === null;
  }

  /**
   * Check if object is not null
   * @param value
   * @returns true if not null otherwise false
   */
  public static isNotNilObject(value: any): boolean {
    return (
      this.isObject(value) &&
      this.isNotNil(value) &&
      !this.isArray(value) &&
      Object.keys(value).length > 0
    );
  }

  /**
   * Check if object is null or empty
   * @param value
   * @returns true if not null otherwise false
   */
  public static isNilObject(value: any): boolean {
    return (
      this.isNil(value) &&
      isEmpty(value) &&
      !this.isArray(value) &&
      Object.keys(<any>value).length === 0 &&
      !this.isDateObj(value)
    );
  }

  /**
   * Check if object is not null
   * @param value
   * @returns true if not null otherwise false
   */
  public static isNotNilArray(value: any): boolean {
    return this.isArray(value) && this.isNotNil(value) && value.length > 0;
  }

  /**
   * Check if string value is not empty
   * @param value
   * @returns true if not empty otherwise false
   */
  public static isNonEmptyString(value: any): boolean {
    return typeof value === 'string' && value !== '';
  }

  /**
   * Check if string value is empty
   * @param value
   * @returns true if empty otherwise false
   */
  public static isEmptyString(value: any): boolean {
    return typeof value === 'string' && value === '';
  }

  /**
   * Check if value is a template
   * @param value
   * @returns true if value is an instace of template otherwise false
   */
  public static isTemplateRef(value: any): boolean {
    return value instanceof TemplateRef;
  }

  /**
   * Check if value is a number
   * @param value
   * @returns true if number otherwise false
   */
  public static isNumber(value: any): boolean {
    return typeof value === 'number';
  }

  /**
   * Check if value is a string
   * @param value
   * @returns true if string otherwise false
   */
  public static isString(value: any): boolean {
    return typeof value === 'string';
  }

  /**
   * Check if value is boolean
   * @param value
   * @returns true if boolean otherwise false
   */
  public static isBoolean(value: any): boolean {
    return typeof value === 'boolean';
  }

  /**
   * Check if value is array
   * @param value
   * @returns true if array otherwise false
   */
  public static isArray(value: any): boolean {
    return Array.isArray(value);
  }

  /**
   * Check if value is an object
   * @param value
   * @returns true if object otherwise false
   */
  public static isObject(value: any): boolean {
    return typeof value === 'object';
  }

  /**
   * Check if value is a date range
   * @param value
   * @returns true if date range otherwise false
   */
  public static isDateRange(value: any): boolean {
    return (
      this.isArray(value) &&
      value.length == 2 &&
      value[0] instanceof Date &&
      value[1] instanceof Date
    );
  }

  /**
   * It checks if the value is a Date object.
   * @param {any} value - any
   * @returns The return value is a boolean.
   */
  public static isDateObj(value: any): boolean {
    return value instanceof Date;
  }

  /**
   * Check if filterValue match with a given item
   * @param item
   * @param filterValue
   * @returns true if filterValue exactly match the item, otherwise false
   */
  public static filterEnumValueMatch(item: any, filterValue: any): boolean {
    let result: boolean = true;
    if (
      filterValue !== undefined &&
      filterValue !== null &&
      filterValue !== ''
    ) {
      result = item.valueOf() == filterValue.valueOf();
    }
    return result;
  }

  /**
   * Check if filterValue exists in a given item
   * @param item
   * @param filterValue
   * @param exactMatch
   * @returns true if filterValue exists in item, otherwise false
   */
  public static filterValueExist(
    item: any,
    filterValue: any,
    exactMatch?: boolean
  ): boolean {
    let result: boolean = true;
    if (
      filterValue !== undefined &&
      filterValue !== null &&
      filterValue !== ''
    ) {
      if (typeof filterValue === 'string')
        exactMatch
          ? (result = item == filterValue)
          : (result = item.indexOf(filterValue) !== -1);
      else if (typeof filterValue === 'boolean') result = item == filterValue;
      else if (typeof filterValue === 'number') result = item == filterValue;
      else if (filterValue instanceof Array) result = false;
    }
    return result;
  }

  /**
   * Get true if objects are equals
   * @param obj1
   * @param obj2
   * @returns boolean true if are equals.
   */
  public static isEqualObjects(obj1: any, obj2: any): boolean {
    return isEqual(obj1, obj2);
  }

  /**
   * Get true if objects are equals by each value
   * @param obj1
   * @param obj2
   * @returns boolean true if are equals.
   */
  public static areEqualsByValue(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) return true;

    if (obj1 && obj2 && typeof obj1 == 'object' && typeof obj2 == 'object') {
      var arrA = Array.isArray(obj1),
        arrB = Array.isArray(obj2),
        i,
        length,
        key;

      if (arrA && arrB) {
        length = obj1.length;
        if (length != obj2.length) return false;
        for (i = length; i-- !== 0; )
          if (!this.areEqualsByValue(obj1[i], obj2[i])) return false;
        return true;
      }

      if (arrA != arrB) return false;

      var dateA = obj1 instanceof Date,
        dateB = obj2 instanceof Date;
      if (dateA != dateB) return false;
      if (dateA && dateB) return obj1.getTime() == obj2.getTime();

      var regexpA = obj1 instanceof RegExp,
        regexpB = obj2 instanceof RegExp;
      if (regexpA != regexpB) return false;
      if (regexpA && regexpB) return obj1.toString() == obj2.toString();

      var keys = Object.keys(obj1);
      length = keys.length;

      if (length !== Object.keys(obj2).length) return false;

      for (i = length; i-- !== 0; )
        if (!Object.prototype.hasOwnProperty.call(obj2, keys[i])) return false;

      for (i = length; i-- !== 0; ) {
        key = keys[i];
        if (!this.areEqualsByValue(obj1[key], obj2[key])) return false;
      }

      return true;
    }

    return obj1 !== obj1 && obj2 !== obj2;
  }

  public static isFunction(obj: any) {
    return !!(obj && obj.constructor && obj.call && obj.apply);
  }
}
