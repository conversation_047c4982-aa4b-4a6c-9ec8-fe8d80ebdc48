# CHANGELOG

Release Schedule

- Patch release: Patch version for routine Bugfix (anytime for urgent bugfix).
- Minor release: New features have been added, Optimization. (Backwards compatible)
- Major version: For Breaking Change, Significant Upgrade. (No Backwards compatible).

# 0.2.0 [17-08-25]

- Managed CRUD for products

# 0.1.9 [12-08-25]

- Added support to German

# 0.1.8 [11-08-25]

- Migration of table and category-list, category-create-update to signal based feature

# 0.1.7 [10-08-25]

- Added complete categories management section with CRUD operations
- Created category-list component with table display and filtering
- Created category-create-update component for adding/editing categories
- Added categories-header component for section navigation
- Implemented categories routing with lazy loading
- Added category section types to current-section enum
- Updated navigation header to include categories section
- Added internationalization support for categories (EN/IT)
- Updated environment configuration for categories
- Enhanced product service tests


# 0.1.6 [06-08-25]

- Fix initialize.service

# 0.1.5 [31-07-25]

- Updated environment with collections base url
- Updated initializer service

# 0.1.4 [30-07-25]

- Refactored product services (renamed `product.service.ts` to `products.service.ts`)
- Added categories and collections services with CRUD operations
- Enhanced product create/update forms with variant management
- Added draft saving functionality for product creation
- Implemented image upload with validation and preview
- Added color picker for product variants
- Enhanced form validation with custom validators
- Fixed `takeUntilDestroyed()` injection context error
- Added new Italian translations for product management
- Updated environment and route configurations

# 0.1.3 [30-07-25]

- Fix in product service

# 0.1.2 [29-07-25]

- Added mock configuration
- Updated **product-list** and .env.mock data (ApiDog)
- Renamed files without **component**
- Fixed NzModalService dependency injection error in ProductListComponent
- Fixed ExpressionChangedAfterItHasBeenCheckedError in product list table rendering

# 0.1.1 [25-07-25]

- Added products section
- Added products interfaces
- Added internationalization support (EN/IT)
- Updated environment for product section

# 0.1.0 [16-07-25]

- Init project
