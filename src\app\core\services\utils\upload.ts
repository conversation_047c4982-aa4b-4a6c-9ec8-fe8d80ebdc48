import { inject, Injectable } from '@angular/core';
import { AbstractControl, ValidationErrors } from '@angular/forms';
import { IFileUpload } from '@models/interfaces/file-upload';
import { NzUploadFile, NzUploadXHRArgs } from 'ng-zorro-antd/upload';
import { throwError } from 'rxjs';
import { ImageService } from './image';

export function ValidateImages(min: number, max: number): any {
  return (images: AbstractControl): ValidationErrors | null => {
    const imageList: NzUploadFile[] = images.value ? images.value : [];
    const validLength = imageList.length >= min && imageList.length <= max;
    const validResponse = imageList.every((image) => image.error === undefined);
    if (validLength && validResponse) {
      return null;
    } else {
      return { error: true };
    }
  };
}

interface ShowUploadListImage {
  showPreviewIcon: boolean;
  showRemoveIcon: boolean;
  hidePreviewIconInNonImage: boolean;
}

/**
 * Servizio per la gestione dell'upload di file e immagini.
 * Fornisce metodi per validare, caricare e gestire file tramite ng-zorro.
 */
@Injectable()
export class UploadService {
  // SERVICES
  private imageService = inject(ImageService);

  // VARIABLES
  private fileList: NzUploadFile[];
  private imageList: IFileUpload[];
  private fileXHRArgs: NzUploadXHRArgs[];
  private showUploadList: ShowUploadListImage;

  constructor() {
    this.fileList = [];
    this.imageList = [];
    this.fileXHRArgs = [];
    this.showUploadList = {
      showPreviewIcon: true,
      showRemoveIcon: true,
      hidePreviewIconInNonImage: true,
    };
  }

  private removeItemFromFileList(file: NzUploadFile): void {
    try {
      const index = this.fileList.findIndex((f) => f === file);
      index > -1
        ? this.fileList.splice(index, 1)
        : throwError({ error: 'Unable to find file item in list' });
    } catch (err) {}
  }

  private removeItemFromImageList(file: NzUploadFile): void {
    try {
      const index = this.imageList.findIndex((i) => i.name === file.name);
      index > -1
        ? this.imageList.splice(index, 1)
        : throwError({ error: 'Unable to image item in list' });
    } catch (err) {}
  }

  // private removeItemFromFileXHRArgs(file: NzUploadFile): void {
  //   try {
  //     const index = this.fileXHRArgs.findIndex(
  //       f => f.file.name === file.name && f.file.size === file.size
  //     );
  //     index > -1
  //       ? this.fileXHRArgs.splice(index, 1)
  //       : throwError({ error: 'Unable to fileXHRArgs item in list' });
  //   } catch (err) {
  //     console.log(err);
  //   }
  //   console.log('removeItemFromFileXHRArgs -----------', this.fileXHRArgs);
  // }

  // private buildRequests(id: string): Observable<IFileProgress>[] {
  //   const requests$ = (): Observable<IFileProgress>[] => {
  //     let array = [];
  //     for (let imageReq of this.fileXHRArgs) {
  //       array.push(this.imageService.uploadFile(imageReq, id));
  //     }
  //     return array;
  //   };
  //   return requests$();
  // }

  // private noNewImageRequests(): Observable<IFileProgress>[] {
  //   const requests$ = (): Observable<IFileProgress>[] => {
  //     let array = [];
  //     for (let image of this.imageList) {
  //       const fileProgress: IFileProgress = {
  //         file: {
  //           name: image.name,
  //           url: image.url
  //         },
  //         fileUpload: null,
  //         progress: null
  //       };
  //       array.push(
  //         new Observable(observer => {
  //           observer.next(fileProgress);
  //           observer.complete();
  //         })
  //       );
  //     }
  //     return array;
  //   };
  //   return requests$();
  // }

  // public create(id: string): Observable<IFileProgress>[] {
  //   return this.buildRequests(id);
  // }

  // public update(id: string): Observable<IFileProgress>[] {
  //   return this.fileXHRArgs.length > 0
  //     ? this.buildRequests(id)
  //     : this.noNewImageRequests();
  // }

  // public pushNewFileRequest(file: NzUploadXHRArgs): void {
  //   this.FileXHRArgs.push(file);
  // }

  public removeFiles(file: NzUploadFile): void {
    this.removeItemFromFileList(file);
    this.removeItemFromImageList(file);
    // this.removeItemFromFileXHRArgs(file);
  }

  public clean(): void {
    this.fileList = [];
    this.imageList = [];
    this.fileXHRArgs = [];
  }

  public get FileList(): NzUploadFile[] {
    return this.fileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.fileList = list;
  }

  public get ImageList(): IFileUpload[] {
    return this.imageList;
  }

  public set ImageList(list: IFileUpload[]) {
    this.imageList = list;
  }

  public get FileXHRArgs(): NzUploadXHRArgs[] {
    return this.fileXHRArgs;
  }

  public get ShowUploadList(): ShowUploadListImage {
    return this.showUploadList;
  }

  public set ShowUploadList(options: ShowUploadListImage) {
    this.showUploadList = options;
  }
}
