import { cloneDeep, get, keys, set, unset } from 'lodash';
import { CheckUtils } from './check';

/**Objects Utils */
export class ObjectUtils {
  /**
   * Return object keys as array
   * @param obj
   * @returns boolean true if are equals.
   */
  public static getKeys(obj: any) {
    return keys(obj);
  }

  /**
   * Get Property value of an object
   * @param obj
   * @param key
   * @returns property value.
   */
  public static getPropertyByName<T, K extends keyof T>(obj: T, key: K) {
    return get(obj, key);
  }

  /**
   * Get Property value of an object
   * @param obj
   * @param key
   * @returns property value.
   */
  public static removePropertyByName<T, K extends keyof T>(obj: T, key: K) {
    unset(obj, key);
  }

  /**
   * Set Property value of an object
   * @param obj
   * @param key
   * @param value
   */
  public static setPropertyByName<T, K extends keyof T>(
    obj: T,
    key: K,
    value: any
  ) {
    set(<object>(<unknown>obj), key, value);
  }

  /**
   * Get Property value of an object
   * @param obj
   * @param key
   * @returns true if propery exists, otherwise false.
   */
  public static propertyExists<T, K extends keyof T>(obj: T, key: K): boolean {
    let result: boolean = false;
    if (CheckUtils.isNotNil(obj))
      result = Object.prototype.hasOwnProperty.call(obj, key);
    return result;
  }

  /**
   * Return a copy of objects
   * @returns obj
   * @param source
   */
  public static shallowCopyObject<T>(source: T): T {
    return cloneDeep(source);
  }
}
