import { inject, Injectable } from '@angular/core';
import { CheckUtils } from '@core/utils/check';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';

const _LANGUAGE: string = '_language';

/**
 * Servizio per la gestione della lingua dell'applicazione e delle traduzioni.
 */
@Injectable({
  providedIn: 'root',
})
export class LanguageService {
  // SERVICES
  private translateService = inject(TranslateService);

  // OBSERVABLES
  language$: Subject<languageCodeType> = new Subject();

  // VARIABLES
  languages: ILanguage[] = [];
  currentLanguage?: ILanguage;

  /**
   * Configura la lista delle lingue disponibili.
   * @param languages Array di lingue supportate
   * @returns void
   */
  configureLang(languages: ILanguage[]): void {
    this.languages = languages;
  }

  /**
   * Restituisce la traduzione immediata di una chiave.
   * @param key Chiave di traduzione
   * @returns Stringa tradotta
   */
  instantTranslate(key: string): string {
    return this.translateService.instant(key);
  }

  /**
   * Carica la lingua corrente dall'archivio locale o imposta quella di default.
   * @returns Promise risolta quando la lingua è caricata
   */
  loadLanguage(): Promise<Event> {
    return new Promise<Event>((resolve, _reject) => {
      let storageLanguage: string = localStorage.getItem(_LANGUAGE) || '';
      if (!CheckUtils.isNullUndefinedOrEmpty(storageLanguage)) {
        this.currentLanguage = this.#getLanguageByCode(storageLanguage);
        this.setLanguage(this.currentLanguage);
      } else {
        this.#setDefaultLanguage();
      }
      resolve(<any>true);
    });
  }

  #setDefaultLanguage() {
    let l = this.languages?.find((l) => l.default);
    this.setLanguage(l!);
  }

  /**Get Language Code from a string formatted as 'code-countrycode'. Example 'it-IT' returns 'it' */
  #getLanguageByCode(code: string): ILanguage {
    return this.languages?.find((l) => l.code == code)!;
  }

  setLanguage(language: ILanguage): void {
    this.languages.forEach((l) => (l.active = false));
    language.active = true;
    this.currentLanguage = language;
    this.#updateTranslateService(language);
    this.#setCookieLanguage(language.code);
    this.language$.next(language.code);
  }

  #updateTranslateService(language: ILanguage) {
    this.translateService.setDefaultLang(language.code);
    this.translateService.use(language.code);
  }

  #setCookieLanguage(value: string): void {
    localStorage.setItem(_LANGUAGE, value);
  }
}

/** Define the language object */
export type ILanguage = {
  name?: string;
  code: languageCodeType;
  enabled?: boolean;
  default?: boolean;
  countryCode?: languageCountryCodeType;
  active?: boolean;
  icon?: string;
};

/** Define the LanguageCode Type */
export enum languageCodeType {
  it = 'it',
  en = 'en',
  de = 'de',
}

/** Define thee LanguageCountryCode Type */
export enum languageCountryCodeType {
  IT = 'IT',
  GB = 'GB',
  DE = 'DE',
}

/** Define thee LanguageName Type */
export enum languageNameType {
  it = 'Italiano',
  en = 'English',
  de = 'Deutsch',
}
