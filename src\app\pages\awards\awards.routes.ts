import { Routes } from '@angular/router';
import { crudActionType } from '@models/enums/crud-action-type';
import { AwardCreateUpdate } from './award-create-update/award-create-update';
import { AwardList } from './award-list/award-list';

export const AWARDS_ROUTES: Routes = [
  {
    path: '',
    component: AwardList,
  },
  {
    path: 'create',
    component: AwardCreateUpdate,
    data: { crudType: crudActionType.create },
  },
  {
    path: ':id',
    component: AwardCreateUpdate,
    data: { crudType: crudActionType.update },
  },
];
