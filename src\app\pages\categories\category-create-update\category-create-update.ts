import { Ng<PERSON>tyle } from '@angular/common';
import {
  afterNextRender,
  Component,
  DestroyRef,
  inject,
  Signal,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CategoriesService } from '@core/services/http/categories';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { HeaderService } from '@core/services/utils/header';
import { ImageService } from '@core/services/utils/image';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { UploadService, ValidateImages } from '@core/services/utils/upload';
import { FormUtils } from '@core/utils/form';
import { getBase64Async } from '@core/utils/image';
import { log } from '@core/utils/logger';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { ICategory } from '@models/interfaces/category';
import {
  IImageSize,
  ShowUploadListImage,
} from '@models/interfaces/file-upload';
import { TranslateModule } from '@ngx-translate/core';
import { InputCheckboxComponent } from '@shared/inputs/input-checkbox/input-checkbox';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import {
  NzFormControlComponent,
  NzFormDirective,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzModalComponent } from 'ng-zorro-antd/modal';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import {
  NzUploadComponent,
  NzUploadFile,
  NzUploadXHRArgs,
} from 'ng-zorro-antd/upload';
import { delay, of, switchMap, tap } from 'rxjs';

enum ValidatorsField {
  IMAGES = 'images',
}

const IMAGE_LIMITS: IImageSize = {
  width: {
    min: 200,
    max: 2000,
  },
  height: {
    min: 200,
    max: 2000,
  },
  size: {
    kilobytes: 16,
    megabytes: 16,
    gigabytes: 16,
  },
};

@Component({
  selector: 'app-category-create-update',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NzFormDirective,
    NzSpaceComponent,
    NzButtonComponent,
    NzSpaceItemDirective,
    TranslateModule,
    InputGenericComponent,
    NzSpinComponent,
    NzRowDirective,
    NzColDirective,
    InputCheckboxComponent,
    NzFormControlComponent,
    NzFormItemComponent,
    NzFormLabelComponent,
    NzIconDirective,
    NzPopoverDirective,
    NzUploadComponent,
    NzModalComponent,
    NgStyle,
  ],
  providers: [UploadService],
  templateUrl: './category-create-update.html',
  styleUrl: './category-create-update.less',
})
export class CategoryCreateUpdateComponent extends CreateUpdateItem {
  // SERVICES
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private uploadService = inject(UploadService);
  private imageService = inject(ImageService);
  private categoryService = inject(CategoriesService);
  private router = inject(Router);
  private messageService = inject(MessageService);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);
  private fb = inject(FormBuilder);
  private breadcrumbService = inject(BreadcrumbService);

  protected baseForm!: FormGroup;
  // Convert to signals
  protected saveButtonTitle = signal<string>('CATEGORIES.saveCategory');
  protected abortButtonTitle = signal<string>('CATEGORIES.deleteCategory');
  protected crudMode = signal<crudActionType>(crudActionType.create);
  protected crudActionType = crudActionType;
  protected isValidForm = signal<boolean>(false);
  protected categoryId = signal<string>('');
  protected loading = signal<boolean>(true);
  protected category: Signal<ICategory | undefined> =
    this.categoryService.category$;

  /**
   * Inizializza il servizio di upload con le immagini esistenti della categoria.
   * Popola FileList e ImageList con i dati dal form.
   * @returns void
   */
  private initUploadService(): void {
    this.uploadService.FileList = FormUtils.populateImages(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
    this.uploadService.ImageList = FormUtils.populateField(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
  }

  protected showInHomeList = [
    { label: 'Mostra nella home', value: 'show', checked: true },
  ];

  protected previewImage: string | undefined = '';
  protected previewVisible = false;

  // VIEWCHILD
  protected tplButton = viewChild<TemplateRef<any>>('tplButton');

  /**
   * Costruttore del componente category create/update.
   * Inizializza il form, gestisce i parametri di routing e configura l'header.
   * @returns void
   */
  constructor() {
    super();
    this.initForm();
    this.headerService.setCurrentSection(currentSectionType.categoryDetail);
    this.route.paramMap
      .pipe(
        takeUntilDestroyed(),
        switchMap((params) => {
          this.categoryId.set(<string>params.get('id'));
          return this.route.data.pipe(
            tap((data) => {
              this.crudMode.set(<crudActionType>data['crudType']);
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();

    afterNextRender(() => {
      this.headerService.setCurrentSection(
        currentSectionType.categoryDetail,
        this.tplButton(),
      );
    });
  }

  public get FileList(): NzUploadFile[] {
    return this.uploadService.FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.uploadService.FileList = list;
  }

  public get imagesField(): AbstractControl {
    return this.baseForm.get(ValidatorsField.IMAGES)!;
  }

  public get Limits(): IImageSize {
    return IMAGE_LIMITS;
  }

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64Async(file.originFileObj!);
    }
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };

  public removeItem = (file: NzUploadFile): boolean => {
    this.uploadService.removeFiles(file);
    this.imagesField.patchValue(this.uploadService.FileList);
    this.uploadService.FileList.length <= 0 ||
    !this.uploadService.FileList.every((image) => image.error === undefined)
      ? this.isValidForm.set(false)
      : this.isValidForm.set(true);
    return false;
  };

  public showUploadList: ShowUploadListImage = {
    showPreviewIcon: true,
    showRemoveIcon: true,
    hidePreviewIconInNonImage: true,
  };
  public uploadRequest = (item: NzUploadXHRArgs) => {
    return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
      next: (image) => {
        log('UPLAOD OK', image);
        of(image)
          .pipe(delay(200))
          .subscribe(() => {
            this.imagesField.patchValue(this.uploadService.FileList);
          });
      },
      error: (err) => {
        log('ERROR IMAGE', err);
      },
      complete: () => {
        log('IMAEG FILE :IST', this.uploadService.FileList);
      },
    });
  };

  public setMediaUploadHeaders = () => {
    return {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
  };

  /**
   * Inizializza il form reattivo con i campi e le validazioni per la categoria.
   * Include validazioni per nome, visibilità in home e immagini.
   * @returns void
   */
  private initForm() {
    this.baseForm = this.fb.group({
      id: ['', [Validators.nullValidator]],
      name: ['', [Validators.required]],
      showInHome: [true, [Validators.nullValidator]],
      images: ['', [Validators.required, ValidateImages(1, 5)]],
    });
  }

  /**
   * Imposta la modalità CRUD (create o update) in base ai dati di routing.
   * @returns void
   */
  override setCrudMode() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }

  /**
   * Configura il componente per la modalità di creazione di una nuova categoria.
   * Resetta il form, imposta i titoli dei pulsanti e mostra il breadcrumb.
   * @returns void
   */
  override setCreateMode() {
    this.baseForm.reset();
    this.initForm();
    this.categoryService.setCategory(undefined);
    this.saveButtonTitle.set('CATEGORIES.createCategory');
    this.abortButtonTitle.set('abort');
    this.breadcrumbService.setIsLoading(false);
    this.loading.set(false);
  }

  /**
   * Configura il componente per la modalità di aggiornamento di una categoria esistente.
   * Carica i dati della categoria tramite HTTP, popola il form e configura il breadcrumb.
   * @returns void
   */
  override setUpdateMode() {
    this.saveButtonTitle.set('CATEGORIES.updateCategory');
    this.abortButtonTitle.set('CATEGORIES.deleteCategory');

    this.categoryService.readOne(this.categoryId()).subscribe({
      next: (res) => {
        this.fillFormData();
        this.initUploadService();
        const breadcrumbData = res.data?.name;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.router.navigateByUrl('/categories');
      },
    });
  }

  /**
   * Popola il form con i dati della categoria caricata dal server.
   * Imposta i valori per id, nome, visibilità in home e immagini.
   * @returns void
   */
  fillFormData() {
    this.baseForm.get('id')!.setValue(this.category()!.id);
    this.baseForm.get('name')!.setValue(this.category()!.name);
    this.baseForm.get('showInHome')!.setValue(this.category()!.showInHome);
    this.baseForm.get('images')!.setValue(this.category()!.images);
  }

  /**
   * Hook di inizializzazione del componente.
   * Avvia l'osservazione dei cambiamenti del form.
   * @returns void
   */
  ngOnInit(): void {
    this.observeFormChanges();
  }

  /**
   * Osserva i cambiamenti del form e gestisce gli errori di upload delle immagini.
   * Imposta isValidForm in base alla validità del form e agli errori di upload.
   * @returns void
   */
  observeFormChanges() {
    this.imageService.uploadOnError$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((item) => {
        this.isValidForm.set(false);
      });

    this.baseForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.isValidForm.set(this.baseForm.valid);
      });
  }

  /**
   * Gestisce il click del pulsante di annullamento/eliminazione.
   * In modalità create: naviga alla lista categorie.
   * In modalità update: mostra modal di conferma per eliminazione.
   * @returns void
   */
  onAbortClick() {
    // this.loading.set(true);
    switch (this.crudMode()) {
      case crudActionType.create:
        this.router.navigateByUrl('/categories');
        break;

      case crudActionType.update:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.categoryService.delete(this.categoryId()).subscribe({
              next: () => {
                this.loading.set(false);
                this.router.navigateByUrl('/categories');
                this.messageService.addSuccessMessage(
                  'CATEGORIES.deleteSuccess',
                );
                log(`CATEGORY ID: ${this.categoryId()} - Eliminato`);
              },
            });
          },
          title: 'CATEGORIES.confirmDeleteTitle',
          subtitle: 'CATEGORIES.confirmDeleteSubtitle',
        });
        break;
    }
  }

  /**
   * Gestisce il click del pulsante di salvataggio.
   * Determina se eseguire creazione o aggiornamento in base alla modalità CRUD.
   * @returns void
   */
  onDataSaveClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.onDataSubmit();
        break;
      case crudActionType.update:
        this.onDataUpdate();
        break;
    }
  }

  /**
   * Gestisce la creazione di una nuova categoria.
   * Invia i dati del form al server tramite HTTP POST.
   * @returns void
   */
  override onDataSubmit() {
    this.loading.set(true);
    const category = this.baseForm.value;
    FormUtils.removeObjectNullProperties(category);
    this.messageService.addLoadingMessage('loading');
    this.categoryService.create(category).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('CATEGORIES.createSuccess');
        this.loading.set(false);
        this.router.navigate(['categories', res.data!.id]);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Gestisce l'aggiornamento di una categoria esistente.
   * Invia i dati modificati al server tramite HTTP PUT e aggiorna il breadcrumb.
   * @returns void
   */
  override onDataUpdate() {
    this.loading.set(true);
    const category = this.baseForm.value;
    this.messageService.addLoadingMessage('loading');

    this.categoryService.update(this.categoryId(), category).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('CATEGORIES.updateSuccess');
        const breadcrumbData = res.data?.name;
        this.breadcrumbService.setBreadcrumbData(breadcrumbData);
        this.breadcrumbService.setIsLoading(false);
        this.loading.set(false);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  /**
   * Cleanup del componente alla distruzione.
   * Resetta il breadcrumb service allo stato iniziale.
   * @returns void
   */
  ngOnDestroy(): void {
    this.breadcrumbService.reset();
  }
}
