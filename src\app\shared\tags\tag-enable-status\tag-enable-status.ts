import { Component, OnChanges, input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzTagComponent } from 'ng-zorro-antd/tag';

@Component({
  selector: 'app-tag-enable-status',
  standalone: true,
  imports: [NzTagComponent, NzIconDirective, TranslateModule],
  templateUrl: './tag-enable-status.html',
  styleUrl: './tag-enable-status.less',
})
export class TagEnableStatusComponent implements OnChanges {
  readonly value = input<boolean>(undefined);
  protected text: string;
  protected icon: string;
  protected color: string;

  constructor() {}

  ngOnChanges(): void {
    this.setTag();
  }

  setTag() {
    switch (true) {
      case !!this.value():
        this.text = 'TAGS.enabled';
        this.icon = 'unlock';
        this.color = 'success';
        break;
      default:
        this.text = 'TAGS.disabled';
        this.icon = 'lock';
        this.color = 'error';
        break;
    }
  }
}
