import { UpperCasePipe } from '@angular/common';
import { Component, signal, inject } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/http/auth';
import { CustomValidators } from '@core/validators/custom.validator';
import { TranslateModule } from '@ngx-translate/core';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { InputPasswordComponent } from '@shared/inputs/input-password/input-password';
import { NzFormDirective } from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzTypographyModule } from 'ng-zorro-antd/typography';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    InputGenericComponent,
    InputPasswordComponent,
    FormsModule,
    ReactiveFormsModule,
    NzFormDirective,
    NzSpinComponent,
    TranslateModule,
    UpperCasePipe,
    NzColDirective,
    NzRowDirective,
    NzTypographyModule,
    SimpleButtonComponent,
  ],
  templateUrl: './login.html',
  styleUrl: './login.less',
  host: {
    '(keydown.enter)': 'onEnterClick()',
  },
})
export class LoginComponent {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);

  protected loginForm: FormGroup<{
    email: FormControl<string>;
    password: FormControl<string>;
    remember: FormControl<boolean>;
  }>;
  protected loginError = signal<boolean>(false);
  protected isLoading = signal<boolean>(false);

  constructor() {
    trackEvent('login_page');
    this.initForm();
  }

  initForm() {
    this.loginForm = this.fb.group({
      email: [
        '',
        [
          Validators.required,
          Validators.minLength(5),
          Validators.maxLength(50),
          Validators.pattern(CustomValidators.emailRegex),
        ],
      ],
      password: [
        '',
        [
          Validators.required,
          Validators.minLength(9),
          Validators.maxLength(50),
        ],
      ],
      remember: [false, Validators.required],
    });
  }

  onLoginClick() {
    this.isLoading.set(true);
    const email = this.loginForm.value.email;
    const password = this.loginForm.value.password;
    this.authService.login(email, password).subscribe({
      next: () => {
        this.router.navigateByUrl('');
        this.isLoading.set(false);
      },
      error: (err) => {
        console.log('error', err);
        this.loginError.set(true);
        this.isLoading.set(false);
        setTimeout(() => {
          this.loginError.set(false);
        }, 3000);
      },
    });
  }

  onRegisterClick() {
    this.router.navigateByUrl('/auth/register');
  }

  onForgotPasswordClick() {
    this.router.navigateByUrl('/auth/forgot-password');
  }

  onEnterClick() {
    if (this.loginForm.valid) this.onLoginClick();
  }
}
