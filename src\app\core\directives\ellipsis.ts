import {
  Directive,
  ElementRef,
  inject,
  Input,
  output,
  SimpleChanges,
  TemplateRef,
} from '@angular/core';
import { NzResizeService } from 'ng-zorro-antd/core/services';
import { NgStyleInterface, NzTSType } from 'ng-zorro-antd/core/types';
import {
  NzTooltipBaseDirective,
  NzToolTipComponent,
  NzTooltipTrigger,
  PropertyMapping,
} from 'ng-zorro-antd/tooltip';
import { takeUntil } from 'rxjs';

@Directive({
  selector: '[ellipsis]',
  standalone: true,
  host: {
    '[class.ant-tooltip-open]': 'visible',
  },
})
export class EllipsisDirective extends NzTooltipBaseDirective {
  // Required abstract properties from NzTooltipBaseDirective
  arrowPointAtCenter?: boolean = false;
  directiveTitle?: NzTSType | null = null;
  directiveContent?: NzTSType | null = null;
  content?: NzTSType | null = null;
  override trigger?: NzTooltipTrigger = 'hover';
  placement?: string | string[] = 'top';
  override origin?: ElementRef = this.elementRef;
  visible?: boolean = false;
  mouseEnterDelay?: number = 0.15;
  mouseLeaveDelay?: number = 0.1;
  overlayClassName?: string;
  overlayStyle?: NgStyleInterface;
  overlayClickable?: boolean = true;

  // SERVICES
  private resizeService = inject(NzResizeService);

  private titleBackup?: NzTSType | null;
  private timeout: any;

  /* Adding a new property to the component. */
  // TODO: Skipped for migration because:
  //  This input overrides a field from a superclass, while the superclass field
  //  is not migrated.
  @Input('tooltipTitle') override title?: string | TemplateRef<void> | null;
  /* Creating an event emitter that emits a boolean value. */
  readonly onEllipsisChange = output<boolean>();

  constructor() {
    super(NzToolTipComponent);
    this.resizeService
      .connect()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.checkIsEllipsis();
      });
  }

  // constructor(
  //   elementRef: ElementRef,
  //   hostView: ViewContainerRef,
  //   renderer: Renderer2,
  //   private cdr: ChangeDetectorRef,
  //   private resizeService: NzResizeService,
  //   @Host() @Optional() noAnimation?: NzNoAnimationDirective
  // ) {
  //   super(elementRef);
  //   this.elementRef.nativeElement.style.whiteSpace = 'nowrap';
  //
  // }

  override ngOnChanges(changes: SimpleChanges): void {
    const { title } = changes;
    this.titleBackup = this.title;
    if (title && title.currentValue) {
      this.timeout = setTimeout(() => {
        this.checkIsEllipsis();
      }, 1);
    }
  }

  protected override getProxyPropertyMap(): PropertyMapping {
    return {
      ...super.getProxyPropertyMap(),
      tooltipTitle: ['nzTitle', () => this.title],
    };
  }

  /**
   * If the client width is less than the scroll width, then the element is ellipsis
   */
  private checkIsEllipsis() {
    if (
      this.elementRef.nativeElement.clientWidth <
      this.elementRef.nativeElement.scrollWidth
    ) {
      if (this.titleBackup !== undefined) {
        this.component.nzTitle = this.titleBackup;
      } else {
        this.component.nzTitle = null;
      }
      this.elementRef.nativeElement.style.overflow = 'hidden';
      this.elementRef.nativeElement.style.textOverflow = 'ellipsis';
      this.onEllipsisChange.emit(true);
    } else {
      this.component.nzTitle = null;
      this.elementRef.nativeElement.style.overflow = '';
      this.elementRef.nativeElement.style.textOverflow = '';
      this.onEllipsisChange.emit(false);
    }
  }

  ngOnDestroy(): void {
    clearTimeout(this.timeout);
  }
}
