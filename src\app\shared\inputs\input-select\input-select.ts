import { NgStyle } from '@angular/common';
import { Component, Input, input, output } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CheckUtils } from '@core/utils/check';
import { TranslateModule } from '@ngx-translate/core';
import {
  NzFormControlComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { BehaviorSubject, debounceTime } from 'rxjs';

@Component({
  selector: 'app-input-select',
  standalone: true,
  imports: [
    NzSelectModule,
    FormsModule,
    ReactiveFormsModule,
    NzFormLabelComponent,
    NzFormControlComponent,
    TranslateModule,
    NgStyle,
  ],
  templateUrl: './input-select.html',
  styleUrl: './input-select.less',
})
export class InputSelectComponent {
  readonly parentForm = input<FormGroup | any>(undefined);
  readonly controlName = input<string>(undefined);
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() label: string;
  readonly placeholder = input<string>(undefined);
  readonly prefixIcon = input<string>(undefined);
  readonly mode = input<'multiple' | 'tags' | 'default'>('default');
  readonly hideSelectedOptions = input<boolean>(false);
  readonly size = input<'large' | 'small' | 'default'>('default');
  readonly disabled = input<boolean>(false);
  readonly optionList = input<
    | {
        label: string;
        value: any;
      }[]
    | any
  >(undefined);
  readonly configKey = input<{
    label: string;
    value: string;
  }>({
    label: 'label',
    value: 'value',
  });
  readonly maxTagCount = input<number>(5);
  readonly showSearch = input<boolean>(false);
  readonly serverSearch = input<boolean>(false);
  readonly allowClear = input<boolean>(false);
  readonly labelPosition = input<'left' | 'top'>('left');
  readonly onSearch = output<string>();

  protected searchChange$ = new BehaviorSubject('');

  ngOnInit(): void {
    this.searchChange$.pipe(debounceTime(500)).subscribe((data) => {
      if (!CheckUtils.isNullUndefinedOrEmpty(data)) this.onSearch.emit(data);
    });
  }

  isRequired() {
    return this.parentForm()
      .get(this.controlName())
      .hasValidator(Validators.required);
  }

  onSearchClick(value: any) {
    this.searchChange$.next(value);
  }

  compareFn = (o1: any, o2: any): boolean => {
    return o1 && o2
      ? o1[this.configKey().value] === o2[this.configKey().value]
      : o1 === o2;
  };
}
