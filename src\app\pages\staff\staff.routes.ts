import { Routes } from '@angular/router';

import { crudActionType } from '@models/enums/crud-action-type';
import { StaffCreateUpdateComponent } from './staff-create-update/staff-create-update';
import { StaffListComponent } from './staff-list/staff-list';

export const STAFF_ROUTES: Routes = [
  { path: '', component: StaffListComponent },
  {
    path: 'create',
    component: StaffCreateUpdateComponent,
    data: { crudType: crudActionType.create },
    children: [{ path: '', component: StaffCreateUpdateComponent }],
  },
  {
    path: ':id',
    component: StaffCreateUpdateComponent,
    data: { crudType: crudActionType.update },
    children: [{ path: '', component: Staff<PERSON>reateUpdateComponent }],
  },
];
