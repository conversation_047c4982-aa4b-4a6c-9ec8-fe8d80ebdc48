@import "mixin";

.logout {
  color: #a61d24;
}

.themeMixin({
  :host {

    .top-bar-desktop {
      padding-right: 8px;

      .top-bar-column {
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }

      .btn-top-bar,
      .avatar-top-bar,
      .switch-top-bar {
        padding: 0 16px;
      }

      .avatar-top-bar,
      .switch-top-bar {
        margin-top: -2px;
      }

      .btn-top-bar {
        font-size: 24px;

        .icon {
          font-size: 24px;
        }
      }

      /* Buttons */

      .btn-layout {
        color: #555555;
      }

      .avatar-top-bar:hover {
        margin-bottom: -4px;
      }

      .btn-layout:hover {
        background-color: @top-bar-actions-hover;
        cursor: pointer;
        border-radius: 6px;
        height: 70%;
        display: flex;
        align-items: center;

        i {
          color: @primary-color;
        }

      }

      .user-avatar {
        color: @white;
        background-color: @primary-color;
        padding: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .top-bar-mobile {
      display: none;
    }




    @media screen and (max-width: 768px) {
      .top-bar-desktop {
        display: none;
      }

      .top-bar-mobile {
        display: flex;
        height: 64px;

        div[nz-icon] {
          transform: scale(1.4)
        }


        .divider {
          display: flex;
          justify-content: flex-end;
          background-color: @header-background;
          border-bottom: 1px solid #f0f0f0;

          nz-divider {
            height: 40px;
            margin: auto 0;
          }
        }

      }

    }
  }
});

::ng-deep {
  nz-list {
    overflow: hidden !important;
    max-height: 400px !important;

    .ant-list-items {
      // MAX 5 ROWS
      max-height: 310px !important;
      overflow-y: auto !important;
    }

    nz-list-item {
      align-items: flex-start !important;
      overflow-x: hidden;

      .ant-list-item-meta-content {
        width: 100%;
        overflow: hidden;
      }

      .ant-list-item-meta-title {
        line-clamp: 1;
        -webkit-line-clamp: 1;
        width: 250px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    .list-header {
      width: 100%;
      display: flex;
      padding: 0 10px;
      justify-content: space-between;
    }

    .ant-list-item-action {
      margin-left: 24px !important;
    }

    .ant-list-item-meta {
      width: 100% !important;
    }

    nz-list-item.ant-list-item {
      padding: 10px !important;
      width: 100% !important;
    }

    nz-list-footer {
      padding: 10px !important;
    }
  }
}

@media screen and (max-width: 768px) {
  .ant-drawer-body {
    padding: 0;
  }

  .drawer-title {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
}

@-moz-document url-prefix() {
  .ant-list-item-action {
    padding-right: 16px !important;
  }
}
