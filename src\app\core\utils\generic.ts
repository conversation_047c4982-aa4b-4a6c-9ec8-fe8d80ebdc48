export class GenericUtils {
  static AUTH_DATA = "auth_data";
  static session_token = "session_token";
  static user_id = "user_id";
  static menu_status = "menu_status";
  static deviceId = "x-device-id";
  static devicePlatform = "x-platform";
  static tenantID = "x-tenant-id";

  static generateUniqueId(): string {
    // Generate a random part (you can customize the length as needed)
    const randomPart = Math.random().toString(36).substring(2, 10);

    // Get the current timestamp
    const timestamp = new Date().getTime().toString(36);

    // Combine timestamp and random part
    return timestamp + randomPart;
  }

  static ZIndexUtils() {
    let zIndexes = [];

    const generateZIndex = (key, baseZIndex) => {
      let lastZIndex =
        zIndexes.length > 0
          ? zIndexes[zIndexes.length - 1]
          : { key, value: baseZIndex };
      let newZIndex =
        lastZIndex.value + (lastZIndex.key === key ? 0 : baseZIndex) + 2;

      zIndexes.push({ key, value: newZIndex });

      return newZIndex;
    };

    const revertZIndex = (zIndex) => {
      zIndexes = zIndexes.filter((obj) => obj.value !== zIndex);
    };

    const getCurrentZIndex = () => {
      return zIndexes.length > 0 ? zIndexes[zIndexes.length - 1].value : 0;
    };

    const getZIndex = (el) => {
      return el ? parseInt(el.style.zIndex, 10) || 0 : 0;
    };

    return {
      get: getZIndex,
      set: (key, el, baseZIndex) => {
        if (el) {
          el.style.zIndex = String(generateZIndex(key, baseZIndex));
        }
      },
      clear: (el) => {
        if (el) {
          revertZIndex(getZIndex(el));
          el.style.zIndex = "";
        }
      },
      getCurrent: () => getCurrentZIndex(),
    };
  }
}
