import { Pipe, PipeTransform } from '@angular/core';
import { ITableColumn } from '../types/table.column';

@Pipe({
  name: 'hasColumnFilter',
  standalone: true,
  pure: false
})
export class HasColumnFilterPipe implements PipeTransform {
  transform(column: ITableColumn): boolean {
    switch (true) {
      case column.readOnly:
        return false;
      case column.hasSearchFilter:
        return true;
      case column.hasDateFilter:
      case column.hasSingleDateFilter:
        return true;
      case !!column.filterFn:
        return true;
      default:
        return false;
    }
  }
}
