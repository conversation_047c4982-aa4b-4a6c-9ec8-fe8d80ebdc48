import { formatDate } from "@angular/common";
import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
  name: "monthRange",
  standalone: true,
})
export class MonthRangePipe implements PipeTransform {
  transform(date: Date): string {
    const locale = "it-IT";

    // Calculate the first and last day of the month
    const startDate = new Date(date.getFullYear(), date.getMonth(), 1);
    const endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0);

    const start = formatDate(startDate, "d MMMM", locale); // e.g., 1 Gennaio
    const end = formatDate(endDate, "d MMMM yyyy", locale); // e.g., 31 Gennaio 2025

    return `${start} - ${end}`;
  }
}
