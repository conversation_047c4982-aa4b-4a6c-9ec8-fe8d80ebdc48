import { bootstrapApplication } from '@angular/platform-browser';
import { App } from './app/app';
import { appConfig } from './app/app.config';

bootstrapApplication(App, appConfig)
  .then(() => destroySplash()) // rimuove il div quando *tutto* è pronto
  .catch((err) => {
    console.error('Error during bootstrap:', err);
    if (err.message.includes('Loading chunk')) {
      window.location.reload();
    }
    console.error(err);
  });

/**
 * Fa partire l’animazione di uscita e rimuove lo splash
 * solo quando ha finito (evento `animationend`).
 */
export function destroySplash() {
  const splash = document.getElementById('app-splash');
  if (!splash) return;

  // trigger dell’animazione CSS
  splash.classList.add('fade-out');

  // rimuovi il nodo quando l’animazione termina
  const remove = () => splash.remove();
  splash.addEventListener('animationend', remove, { once: true });
}
