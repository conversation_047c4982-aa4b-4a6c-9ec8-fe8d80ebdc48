import { NgStyle } from '@angular/common';
import { Component, input, output, TemplateRef } from '@angular/core';
import { NzButtonComponent, NzButtonType } from 'ng-zorro-antd/button';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';

@Component({
  selector: 'app-popover-button',
  standalone: true,
  imports: [NzButtonComponent, NzPopoverDirective, NzIconDirective, NgStyle],
  templateUrl: './popover-button.html',
  styleUrl: './popover-button.less',
})
export class PopoverButtonComponent {
  tplTitle = input<string | TemplateRef<any>>();
  tplContent = input<string | TemplateRef<any>>();
  icon = input<string>();
  iconOnly = input<boolean>();
  visible = input<boolean>();
  type = input<NzButtonType>('default');
  readonly style = input<{
    [key: string]: any;
  }>(undefined);
  readonly popoverPlacement = input<
    | 'top'
    | 'left'
    | 'right'
    | 'bottom'
    | 'topLeft'
    | 'topRight'
    | 'bottomLeft'
    | 'bottomRight'
    | 'leftTop'
    | 'leftBottom'
    | 'rightTop'
    | 'rightBottom'
  >('top');

  onButtonClick = output();
  visibleChange = output();
}
