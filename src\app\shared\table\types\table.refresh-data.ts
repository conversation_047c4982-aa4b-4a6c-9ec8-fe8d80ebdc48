/** Define the refresh object settings (interval, min, max, ...) */
export interface IRefreshData {
  /** Refresh timer interval (seconds)  */
  interval: number;
  /** Minimum refresh timer interval (seconds) */
  min: number;
  /** Maximum refresh timer interval (seconds) */
  max: number;
  /** The granularity the interval can step through values. Must greater than 0, and be divided by (max - min) . When marks no null, step can be null. */
  step: number;
}
