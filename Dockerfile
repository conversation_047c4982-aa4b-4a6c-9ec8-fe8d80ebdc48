# Use the Node.js Alpine image as the base
FROM node:latest AS build

# Set the working directory
WORKDIR /usr/local/app

# Add the source code to app
COPY ./ /usr/local/app/

## Install dependencies
RUN npm install && npm install -g @angular/cli

# Build the application
RUN npm run build:prod

# Stage 2: Serve app with nginx server

# Use official nginx image as the base image
FROM nginx:latest

RUN rm -rf /usr/share/nginx/html/*

# Copy the custom NGINX configuration file
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy the build output to replace the default nginx contents
COPY --from=build /usr/local/app/public /usr/share/nginx/html

# Expose port 80
EXPOSE 80
