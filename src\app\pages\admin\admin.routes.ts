import { Routes } from '@angular/router';
import { crudActionType } from '@models/enums/crud-action-type';
import { AdminreateUpdate } from './admin-create-update/admin-create-update';
import { AdminList } from './admin-list/admin-list';

export const ADMIN_ROUTES: Routes = [
  {
    path: '',
    component: AdminList,
  },
  {
    path: 'create',
    component: AdminreateUpdate,
    data: { crudType: crudActionType.create },
  },
  {
    path: ':id',
    component: AdminreateUpdate,
    data: { crudType: crudActionType.update },
  },

  // {
  //   path: ":id",
  //   component: StaffOverviewComponent,
  //   children: [
  //     { path: "", redirectTo: "info", pathMatch: "full" },
  //     {
  //       path: "info",
  //       component: StaffCreateUpdateComponent,
  //       data: { crudType: crudActionType.update },
  //     },
  //     {
  //       path: "working-hours",
  //       loadComponent: () =>
  //         import(
  //           "./staff-open-hours/staff-working-hours/staff-working-hours.component"
  //         ).then((m) => m.StaffWorkingHoursComponent),
  //       canDeactivate: [
  //         canDeactivateFormGuard(
  //           (c: StaffWorkingHoursComponent) =>
  //             c.workingHoursComponent().baseForm
  //         ),
  //       ],
  //     },
  //     {
  //       path: "closed-days",
  //       loadComponent: () =>
  //         import(
  //           "./staff-open-hours/staff-closed-days/staff-closed-days.component"
  //         ).then((m) => m.StaffClosedDaysComponent),
  //       canDeactivate: [
  //         canDeactivateFormGuard(
  //           (c: StaffClosedDaysComponent) =>
  //             c.closedDaysComponent().closedDaysForm
  //         ),
  //       ],
  //     },
  //     {
  //       path: "open-days",
  //       loadComponent: () =>
  //         import(
  //           "./staff-open-hours/staff-open-days/staff-open-days.component"
  //         ).then((m) => m.StaffOpenDaysComponent),
  //       canDeactivate: [
  //         canDeactivateFormGuard(
  //           (c: StaffOpenDaysComponent) => c.openDaysComponent().openDaysForm
  //         ),
  //       ],
  //     },
  //
  //   ],
  // },
];
