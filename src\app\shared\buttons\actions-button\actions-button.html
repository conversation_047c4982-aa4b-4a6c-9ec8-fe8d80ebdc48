<nz-dropdown-menu #menu_actions="nzDropdownMenu">
  <ul nz-menu #menu_button_actions style="max-height: 80vh; overflow-y: auto">
    @for (action of actions(); track action.label) {
      @if (
        !action.hidden &&
        (action | isSingleRowActionVisible) &&
        !action.children
      ) {
        <li
          [nzDisabled]="action.disabled"
          nz-menu-item
          (click)="action.disabled ? null : executeActionFn(action)"
          [nzSelected]="action.selected"
          [nzDanger]="action.danger"
        >
          <i nz-icon [nzType]="action.icon" nzTheme="outline"></i
          ><span class="ml-8">{{ action.label | translate }}</span>
        </li>
      } @else if (!action.hidden && (action | isSingleRowActionVisible)) {
        <li
          nz-submenu
          [nzTitle]="tplSingleRowTitle"
          [nzDisabled]="action.disabled"
        >
          <ng-template #tplSingleRowTitle>
            <i nz-icon [nzType]="action.icon"></i>
            <span class="ml-8">{{ action.label | translate }}</span>
          </ng-template>
          <ul style="max-height: 20vh; overflow-y: auto">
            @for (childAct of action.children; track $index) {
              @if (!childAct.hidden && (childAct | isSingleRowActionVisible)) {
                <li
                  nz-menu-item
                  (click)="childAct.disabled ? null : executeActionFn(childAct)"
                  [nzDisabled]="childAct.disabled"
                  [nzSelected]="childAct.selected"
                >
                  @if (childAct.icon) {
                    <i nz-icon [nzType]="childAct.icon"></i>
                  }

                  <span class="ml-8">{{ childAct.label | translate }}</span>
                </li>
              }
            }
          </ul>
        </li>
      }
    }
  </ul>
</nz-dropdown-menu>

@if (menu_button_actions.childElementCount != 0) {
  <button
    nz-button
    nz-dropdown
    [nzType]="type()"
    [nzDropdownMenu]="menu_actions"
    [nzPlacement]="dropdownPlacement()"
    [disabled]="disabled()"
    [ngClass]="_minified() ? 'ant-btn-icon-only' : ''"
  >
    <i nz-icon [nzType]="icon()" nzTheme="outline"></i>
    @if (!_minified()) {
      <span>{{ title() | translate }}</span>
    }
  </button>
}
