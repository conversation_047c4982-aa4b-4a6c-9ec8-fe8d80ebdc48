@import 'mixin';

.themeMixin({
  :host {
    overflow-y: auto;

    .container {
      max-width: 500px;
      overflow-y: auto;
      overflow-x: hidden;

      .subtitle {
        margin-bottom: 24px !important
      }

      .register-form {
        min-width: 300px;
        margin-top: 24px;

        &-forgot {
          text-align: end;
        }
      }

      .register-form-margin {
        margin-bottom: 24px;
      }


      [nz-button] {
        width: 100%;
      }

      h1 {
        margin-bottom: 0;
      }

      h6 {
        margin-bottom: 3rem;
        color: @medium_grey;
      }

      .divider {
        margin-bottom: 16px;
      }

    }
  }
});

.error-message {
  padding: 8px 0;
  text-align: center;
}