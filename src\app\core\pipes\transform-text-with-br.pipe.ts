import { Pipe, PipeTransform } from "@angular/core";

@Pipe({
  name: "transformTextWithBr",
  standalone: true,
})
export class TransformTextWithBrPipe implements PipeTransform {
  transform(value: string): string {
    {
      if (!value) {
        return value;
      }

      // Sostituisce tutti i punti tranne l'ultimo con '.\n'
      // La lookahead `(?=.*\.)` fa sì che il replace avvenga
      // solo se ci sono altri punti dopo quello corrente
      return value.replace(/\.(?=.*\.)/g, ".\n");
    }
  }
}
