import { NgStyle } from '@angular/common';
import { Component, Input, input } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { NzDatePickerComponent } from 'ng-zorro-antd/date-picker';
import {
  NzFormControlComponent,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';

@Component({
  selector: 'app-input-single-datepicker',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NzFormItemComponent,
    NzFormLabelComponent,
    NzFormControlComponent,
    NzDatePickerComponent,
    NgStyle,
    TranslateModule,
  ],

  templateUrl: './input-single-datepicker.html',
  styleUrl: './input-single-datepicker.less',
})
export class InputSingleDatepickerComponent {
  readonly parentForm = input<FormGroup | any>(undefined);
  readonly controlName = input<string>(undefined);
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() label: string;
  readonly placeholder = input<string>(undefined);
  readonly style = input<{
    [key: string]: any;
  }>(undefined);
  readonly labelPosition = input<'left' | 'top'>('left');
  readonly showNow = input<boolean>(false);
  readonly disabledDates = input<(current: Date) => boolean>(undefined);
  readonly nzFormat = input<string>('yyyy-MM-dd');
  readonly allowClear = input<boolean>(false);

  isStartRequired() {
    return this.parentForm()
      .get(this.controlName())
      .hasValidator(Validators.required);
  }

  isEndRequired() {
    return this.parentForm()
      .get(this.controlName())
      .hasValidator(Validators.required);
  }

  handleEndOpenChange(open: boolean): void {
    // log('handleEndOpenChange', open);
  }

  getCombinedStyles(): { [key: string]: any } {
    return {
      ...this.style(),
      display: this.labelPosition() === 'left' ? 'flex' : 'block',
    };
  }
}
