.container {
  align-items: center;
  gap: 8px;

  .radio-label {
    margin-bottom: 0;
    font-weight: 500;
    white-space: nowrap;
  }

  // When label is on top
  &[style*="block"] {
    .radio-label {
      display: block;
      margin-bottom: 8px;
    }
  }

  // When label is on left
  &[style*="flex"] {
    .radio-label {
      margin-right: 8px;
    }
  }

  // Circular radio styles
  .radio-group-circular {

    .radio-option {
      margin-right: 0 !important;
      margin-bottom: 4px !important;
      display: flex !important;
      cursor: pointer;

      .radio-text {
        margin-left: 2px;
        line-height: 1.2;
        vertical-align: middle;
        display: flex;
        align-items: center;
      }


      &:hover {
        color: #1890ff;
      }
    }
  }
}