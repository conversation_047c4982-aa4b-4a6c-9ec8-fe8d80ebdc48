import { HttpErrorResponse, HttpEvent, HttpHandlerFn, HttpRequest, HttpResponse } from '@angular/common/http';
import {
  calculateBase64ImageSize,
  extractBase64FromDataUrl,
  getBase64,
  getImageObject
} from '@app/core/utils/image';
import { IImageSize } from '@models/interfaces/file-upload';
import { Observable, Subscriber, forkJoin, of } from 'rxjs';
import { first, map, switchMap } from 'rxjs/operators';

export const uploadImageInterceptor = (
  req: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> => {
  if (req.url == 'http://localhost:4201/api/fakeImage') {
    return new Observable<any>(observer => {
      return imageUpload(req, observer);
    });
  }
  return next(req);
};

export const imageUpload = (
  req: HttpRequest<any>,
  observer: Subscriber<any>
) => {
  const imageSize = JSON.parse(req.body.get('imageLimits'));
  const imageRequest$ = getBase64(req.body.get('file')).pipe(
    switchMap((base64: string) => getImageObject(base64))
  );

  return forkJoin([imageRequest$, of(imageSize)])
    .pipe(
      first(),
      map(res => {
        return { imageRes: res[0], imageSize: <IImageSize>res[1] };
      })
    )
    .subscribe(
      (res: {
        imageRes: { width: number; height: number; base64: string };
        imageSize: IImageSize;
      }) => {
        const base64Image = extractBase64FromDataUrl(res.imageRes.base64);
        const sizeImage = calculateBase64ImageSize(base64Image!);
        if (sizeImage!.megabytes! <= res.imageSize.size.megabytes!) {
          const responseOK: HttpResponse<{
            body: { ok: true };
            status: number;
            statusText: string;
            url: string;
          }> = new HttpResponse<any>({
            body: { ok: true, base64: res.imageRes.base64 },
            status: 200,
            statusText: 'OK',
            url: 'http://localhost/api/fakeImage'
          });
          observer.next(responseOK);
          observer.complete();
        } else {
          const responseKO: HttpErrorResponse = new HttpErrorResponse({
            error: {
              limits: `Image doesn't respect the limits ${res.imageSize.size.megabytes!.toFixed(
                2
              )} MB. Image size is ${sizeImage.megabytes!.toFixed(2)} MB.`
            },
            status: 500,
            statusText: 'KO',
            url: 'http://localhost/api/fakeImage'
          });
          observer.error(responseKO);
          observer.complete();
        }
      }
    );
};
