@if (!isPageLoading()) {
  <div class="container">
    <nz-spin [nzSpinning]="isLoading()">
      <a (click)="onBackToLoginClick()"
        ><span
          nz-icon
          nzType="client-ui:arrow-left"
          nzTheme="outline"
          class="mr-4"
        ></span
        >{{ "RESET.backToLogin" | translate }}</a
      >
      <h1 class="m-0">{{ "RESET.reset" | translate }}</h1>
      <p class="mb-16">{{ "RESET.subtitle" | translate }}</p>
      <form
        nz-form
        [formGroup]="baseForm"
        class="reset-form"
        [nzLayout]="'vertical'"
      >
        <app-input-password
          [controlName]="'password'"
          [parentForm]="baseForm"
          [placeholder]="'********'"
          [prefixIcon]="'lock'"
          [label]="'RESET.password' | translate"
          [minLength]="9"
          [maxLength]="50"
        ></app-input-password>

        <app-input-password
          [controlName]="'confirmPassword'"
          [parentForm]="baseForm"
          [placeholder]="'********'"
          [prefixIcon]="'lock'"
          [label]="'RESET.confirmPassword' | translate"
          [minLength]="9"
          [maxLength]="50"
        ></app-input-password>
      </form>
      <app-simple-button
        [autoMinify]="false"
        [title]="'RESET.confirmButton' | translate | uppercase"
        [type]="'default'"
        (onButtonClick)="onResetPasswordClick()"
        [disabled]="!baseForm.valid || isLoading()"
        [style]="{ width: '100%' }"
      >
      </app-simple-button>

      @if (resetPassowrdError()) {
        <div class="error-message">
          <span nz-typography nzType="danger">{{
            "FORGOTPASSWORD.error" | translate
          }}</span>
        </div>
      }
    </nz-spin>
  </div>
}
