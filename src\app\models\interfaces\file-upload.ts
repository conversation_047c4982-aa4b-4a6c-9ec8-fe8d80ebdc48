import { NzUploadXHRArgs } from 'ng-zorro-antd/upload';
import { Observable } from 'rxjs';

export interface IFileUpload {
  name: string;
  url: string;
  public_id: string;
}

export interface IFileProgress {
  file: IFileUpload;
  fileUpload: NzUploadXHRArgs;
  progress: Observable<number | undefined>;
}

export interface FileResponse {
  body: { ok: boolean };
  status: number;
  statusText: string;
  url: string;
}

export interface IImageSize {
  width?: { min: number; max: number };
  height?: { min: number; max: number };
  size: IFileSize;
}

export interface ShowUploadListImage {
  showPreviewIcon: boolean;
  showRemoveIcon: boolean;
  hidePreviewIconInNonImage: boolean;
}

export interface IFileSize {
  kilobytes?: number;
  megabytes?: number;
  gigabytes?: number;
}
