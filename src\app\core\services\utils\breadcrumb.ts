import { Injectable, signal } from '@angular/core';

/**
 * Servizio per la gestione dello stato del breadcrumb nell'applicazione.
 *
 * APPROCCIO UTILIZZATO:
 * - Default: isLoading = true (breadcrumb nascosto) per evitare flickering
 * - Componenti con HTTP (Update Mode): mostrano breadcrumb solo dopo caricamento dati
 * - Componenti senza HTTP (Create Mode): chiamano setIsLoading(false) immediatamente
 * - Al ngOnDestroy: reset() riporta allo stato iniziale (loading = true)
 *
 * FLUSSO TIPICO:
 * 1. Caricamento componente → isLoading = true → breadcrumb nascosto
 * 2a. Con HTTP: success → setIsLoading(false) → breadcrumb visibile con dati
 * 2b. Senza HTTP: setCreateMode() → setIsLoading(false) → breadcrumb visibile subito
 * 3. Distruzione → reset() → torna allo stato iniziale
 */
@Injectable({
  providedIn: 'root',
})
export class BreadcrumbService {
  private _breadcrumbData = signal<string | undefined>(undefined);
  public readonly breadcrumbData = this._breadcrumbData.asReadonly();

  private _isLoading = signal<boolean>(true);
  public readonly isLoading = this._isLoading.asReadonly();

  constructor() {}

  /**
   * Imposta il dato del breadcrumb nello stato locale.
   * @param data Stringa breadcrumb da impostare
   * @returns void
   */
  setBreadcrumbData(data: string | undefined): void {
    this._breadcrumbData.set(data);
  }

  /**
   * Imposta lo stato di loading del breadcrumb.
   * @param loading true per mostrare loading, false per nascondere
   * @returns void
   */
  setIsLoading(loading: boolean): void {
    this._isLoading.set(loading);
  }

  /**
   * Resetta sia i dati che lo stato di loading del breadcrumb.
   * Torna allo stato iniziale (loading = true, data = undefined).
   * @returns void
   */
  reset(): void {
    this._breadcrumbData.set(undefined);
    this._isLoading.set(true);
  }
}
