<div
  class="container"
  [ngStyle]="{ display: labelPosition() === 'left' ? 'flex' : 'block' }"
>
  @if (!!label()) {
    <label>
      {{ label() | translate }}
    </label>
  }

  <nz-select
    style="width: 100%"
    [nzPlaceHolder]="placeholder() | translate"
    [nzMode]="mode()"
    [nzSize]="size()"
    [nzMaxTagCount]="maxTagCount()"
    [nzAllowClear]="allowClear()"
    [nzDisabled]="disabled()"
    [nzShowSearch]="showSearch()"
    [nzServerSearch]="serverSearch()"
    [nzNotFoundContent]="notFound()"
    (nzOnSearch)="onSearchClick($event)"
    [(ngModel)]="value"
    [nzDropdownStyle]="showDropdown()"
  >
    @if (!isLoading()) {
      @for (option of optionList(); track option) {
        <nz-option
          [nzHide]="hideSelectedOptions()"
          [nzLabel]="option[configKey().label] | translate"
          [nzValue]="option"
        >
        </nz-option>
      }
    }
    @if (isLoading()) {
      <nz-option nzDisabled nzCustomContent>
        <span nz-icon nzType="loading" class="loading-icon"></span>
        {{ "loadingData" | translate }}
      </nz-option>
    }
  </nz-select>
</div>
