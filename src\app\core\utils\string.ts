export class StringUtils {
  /**
   * Replace browser URL with a replace url if exist. By Default forcedValue is [DYNAMIC_BASE_URL].
   * If has protocol is true, it will not be replaced with host protocol
   * @param url
   * @param hasProtocol
   * @param forcedValue
   * @returns new url
   */
  public static replaceDynamicBaseUrl(
    url: string,
    hasProtocol?: boolean,
    forcedValue?: string
  ) {
    let dynamicBaseUrl = '[DYNAMIC_BASE_URL]';
    forcedValue ? (dynamicBaseUrl = forcedValue) : '';

    if (url.indexOf(dynamicBaseUrl) != -1) {
      if (hasProtocol) url = url.replace(dynamicBaseUrl, window.location.host);
      else {
        let newBaseUrl = window.location.protocol + '//' + window.location.host;
        url = url.replace(dynamicBaseUrl, newBaseUrl);
      }
      return url;
    } else {
      return url;
    }
  }

  /**
   * Get enum Key from enum Value
   * @param myEnum
   * @param enumValue
   * @returns enum Key.
   */
  public static getEnumKeyByEnumValue(
    myEnum: any,
    enumValue: any
  ): string | null {
    let keys = Object.keys(myEnum).filter(x => myEnum[x] == enumValue);
    return keys.length > 0 ? keys[0] : null;
  }

  /**
   * Get enum Keys from enum Value
   * @param myEnum
   * @returns enum Keys.
   */
  public static getEnumKeys(myEnum: any) {
    const result = Object.keys(myEnum)
      .filter(value => isNaN(Number(value)) === false)
      .map(key => myEnum[key]);
    return result;
  }
}
