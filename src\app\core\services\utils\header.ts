import { Injectable, signal, TemplateRef } from '@angular/core';
import { currentSectionType } from '@models/enums/current-section';

/**
 * Servizio per la gestione dello stato dell'header e della sezione corrente nell'applicazione.
 */
@Injectable({
  providedIn: 'root',
})
export class HeaderService {
  protected _data = signal<currentSectionType | undefined>(undefined);
  public data$ = this._data.asReadonly();
  public template = signal<TemplateRef<any> | undefined>(undefined);

  /**
   * Imposta la sezione corrente e il template dell'header.
   * @param data Sezione corrente da impostare
   * @param template TemplateRef opzionale per l'header
   * @returns void
   */
  setCurrentSection(
    data: currentSectionType,
    template?: TemplateRef<any>,
  ): void {
    this._data.set(data);
    this.template.set(template);
  }

  constructor() {}
}
