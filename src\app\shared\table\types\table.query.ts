import { languageCodeType } from '@core/services/utils/language';
import { requestFilterOperatorType } from './table.column';
import { ITableFilterItem } from './table.filter';

/** Creates and initializes a RequestFilter that implements the `IRequestFilter` interface.  */
export class TableQuery implements ITableQuery {
  meta: IRequestMeta;
  filter: IRequestFilter[];
  language: languageCodeType;

  constructor(
    _limit: number,
    _index: number,
    filters: IRequestFilter[],
    language: languageCodeType
  ) {
    this.meta = { page: { size: _limit, index: _index } };
    this.filter = [];
    this.language = language;
  }
}

export interface ITableQuery {
  meta: IRequestMeta;
  filter: IRequestFilter[];
  language: languageCodeType;
}

/** Define the http request filter object */
export interface IRequestFilter {
  operator: requestFilterOperatorType;
  items: IRequestFilterItem[];
}

/** Define the filter item object */
export interface IRequestFilterItem {
  name: string;
  operator: requestFilterOperatorType;
  type: string;
  value: any;
}

/** Define the http request meta object */
export interface IRequestMeta {
  page?: {
    size: number;
    index: number;
  };
  sort?: {
    field: any;
    order: string | 'ascend' | 'descend' | null;
  };
}

export interface IRawTableFilterMetaData {
  queryParams: {
    pageIndex: number;
    pageSize: number;
    sort: {
      key: string;
      value: string | 'ascend' | 'descend' | null;
    }[];
    filter: {
      key: string;
      value: any | any[];
    }[];
  };
  customFilters: ITableFilterItem[];
  requestFilters: ITableFilterItem[];
}
