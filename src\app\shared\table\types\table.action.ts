/** Define the table rows actions */
export interface ITableRowAction {
  /** Context menu action label */
  label?: string;
  /** Context menu action icon */
  icon?: string;
  /** Context menu action visibility */
  hidden?: boolean;
  /** Context menu action active */
  disabled?: boolean;
  /** Callback function executed on action click */
  callbackFn?: Function;
  /** If true, action will be shown as secondary menu action */
  showAsSecondaryAction?: boolean;
  /** Boolean Function for row action visibility (true if visible, otherwise false) */
  visibilityFn?: (row?: any) => boolean;
  /** Define the table rows actions childred*/
  children?: ITableRowAction[];
  /** Used for enable or disable selected style on action */
  selected?: boolean;
  /** set the danger status of action (not implemented)*/
  danger?: boolean;
}
