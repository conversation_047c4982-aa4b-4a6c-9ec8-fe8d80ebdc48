import { Component, inject } from '@angular/core';
import { HeaderService } from '@core/services/utils/header';
import { currentSectionType } from '@models/enums/current-section';

@Component({
  selector: 'app-dashboard',
  imports: [],
  templateUrl: './dashboard.html',
  styleUrl: './dashboard.less',
})
export class Dashboard {
  // SERVICES
  private headerService = inject(HeaderService);

  // ENUMS
  currentSectionType = currentSectionType;

  constructor() {
    this.headerService.setCurrentSection(currentSectionType.dashboard);
  }
}
