@import (multiple) "../../../node_modules/ng-zorro-antd/src/style/themes/dark";
@import (multiple) "./base";

//Main
@primary-color: @deep-red-wine;
@secondary-color: @cyan;
@accent-color: @blue-marin;
@border-radius-base: 6px;
@input-affix-margin: 6px;
@text-color-primary: @white;
@text-color-secondary: @medium_grey;

@header-background: @component-background;
@header-top-border: @white;

//Footer
@footer-text-opacity: rgba(255, 255, 255, 0.548);
@footer-text-light-opacity: rgba(255, 255, 255, 0.705);

//List
@list-header-background: @dark;
@list-footer-background: @dark;

//Page Header
@top-bar-actions-hover: @hover-color;
@circle-radius: @black;
@triangle-radius: @dark;
@avatar-bg: @primary-color;
//
@component-bg: @dark;
@dropdown-menu-bg: @dark;
@sider-menu: @dark;
@border-color-base: #4f4f4f;
@border-color: #303030;

//Layout
@layout-sider-background: @dark;
@layout-header-background: @dark;
@layout-main-page: @dark;

// Typography //
@typography-title-margin-top: 0;
@typography-title-margin-bottom: 0;

//Spin
@spin-bg: #141414;

//Scrollbar
@scrollbar-color: #434343 #262626;

// Sidebar
@info-box-bg: @ultra_strong_gray;
