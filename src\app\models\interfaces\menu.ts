import { roleType } from '@models/enums/role';

export interface ISection {
  level: number;
  id: string;
  title: string;
  groupName?: string;
  divider?: {
    enabled: boolean;
    text?: string;
  };
  icon?: string;
  selected?: boolean;
  disabled?: boolean;
  hidden?: boolean;
  routerLink: string;
  children?: ISection[];
  permission?: string;
  rolePermission: roleType[];
  fields?: string[];
  open?: boolean;
  bottomPosition?: boolean;
}
