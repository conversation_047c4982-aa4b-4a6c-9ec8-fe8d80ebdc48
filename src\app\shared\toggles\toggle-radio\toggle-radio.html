<div
  class="container"
  [ngStyle]="{ display: labelPosition() === 'left' ? 'flex' : 'block' }"
>
  @if (!!label()) {
    <label class="radio-label">
      {{ label() | translate }}
    </label>
  }

  <!-- Radio Button Style -->
  @if (radioType() === "button") {
    <nz-radio-group
      [(ngModel)]="value"
      [nzButtonStyle]="buttonStyle()"
      [nzSize]="size()"
      [nzDisabled]="disabled()"
    >
      @for (option of optionList(); track option[configKey().value]) {
        <label nz-radio-button [nzValue]="option[configKey().value]">
          {{ option[configKey().label] | translate }}
        </label>
      }
    </nz-radio-group>
  }

  <!-- Circular Radio Style -->
  @if (radioType() === "radio") {
    <div class="radio-group-circular">
      <nz-radio-group
        [(ngModel)]="value"
        [nzSize]="size()"
        [nzDisabled]="disabled()"
      >
        @for (option of optionList(); track option[configKey().value]) {
          <label
            nz-radio
            [nzValue]="option[configKey().value]"
            class="radio-option"
          >
            <span class="radio-text">{{
              option[configKey().label] | translate
            }}</span>
          </label>
        }
      </nz-radio-group>
    </div>
  }
</div>
