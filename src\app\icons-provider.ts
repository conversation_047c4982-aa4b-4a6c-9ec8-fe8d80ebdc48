import { EnvironmentProviders, importProvidersFrom } from "@angular/core";
import { NzIconModule } from "ng-zorro-antd/icon";

const icons = [DashOutline];

// Import what you need. RECOMMENDED. ✔️
import { DashOutline } from "@ant-design/icons-angular/icons";

export function provideNzIcons(): EnvironmentProviders {
  return importProvidersFrom(NzIconModule.forRoot(icons));
}

// IMPORTARE TUTTE LE ICONE
// import { EnvironmentProviders, importProvidersFrom } from "@angular/core";
// import * as AllIcons from "@ant-design/icons-angular/icons";
// import { NzIconModule } from "ng-zorro-antd/icon";

// export function provideNzIcons(): EnvironmentProviders {
//   // Ottieni tutte le icone di NG-ZORRO
//   const nzIcons = Object.values(AllIcons);

//   // Inizializza NZ_ICONS con tutte le icone
//   return importProvidersFrom(NzIconModule.forRoot(nzIcons));
// }
