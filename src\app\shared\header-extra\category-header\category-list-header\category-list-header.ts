import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { PopoverButtonComponent } from '@shared/buttons/popover-button/popover-button';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-category-list-header',
  imports: [
    NzSpaceComponent,
    SimpleButtonComponent,
    NzSpaceItemDirective,
    PopoverButtonComponent,
    TranslateModule,
  ],
  templateUrl: './category-list-header.html',
  styleUrl: './category-list-header.less',
})
export class CategoryListHeader {
  private router = inject(Router);

  onAddCategoryClick() {
    this.router.navigateByUrl('/categories/create');
  }
}
