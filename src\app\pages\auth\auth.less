@import 'mixin';

.themeMixin({
  :host {


    .auth {
      display: flex;
      height: 100vh;
      width: 100vw;
      margin: 0;

      background: @component-bg;

      .left-col {
        width: 40%;
        //background-color: @component-bg;
        color: @white;
        //background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 35%, rgba(0, 212, 255, 0.3) 100%), url('/assets/images/left-col-bg.jpg') no-repeat center/cover;

        //& .logo {
        //  // margin-top: 2rem;
        //  width: 100%;
        //  height: 65px;
        //
        //  // padding: 3rem;
        //  // transform: rotate(3deg);
        //
        //  img {
        //    width: 100%;
        //    height: 100%;
        //    object-fit: contain;
        //  }
        //}

        .left-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .main {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 60%;
        height: 100%;
        //background-color: @component-bg;
        margin: 2rem 4rem;
        position: relative;
        overflow: hidden;

        .logo {
          text-align: center;
          margin-bottom: 36px;

          img.dark {
            filter: invert(1);
          }
        }
      }
    }
  }


});

@media screen and (max-width: 992px) {
  .register {
    .left-col {
      display: none !important;
    }

    .main {
      width: 100% !important;
      margin: 20px !important
    }
  }
}