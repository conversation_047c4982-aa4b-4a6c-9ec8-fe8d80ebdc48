@import 'mixin';

.themeMixin({
    :host {

        // .container {
        //   display: flex;
        //   flex-direction: column;
        //   justify-content: space-between;
        //   height: 86%;

        //   .form {
        //     overflow-x: hidden;
        //     padding-left: 1.5rem;
        //     padding-right: 1rem;
        //     margin-right: -0.5rem;

        //     padding-bottom: 22px;
        //     margin-bottom: -22px;
        //   }
        // }

        .info-icon {
            margin-right: 8px;
        }

        .info-image {
            display: grid;
            grid-template-columns: 50% 50%;
            width: 100%;

            .min-max {
                display: flex;
            }
        }

        .image-label {
            margin-left: 6px;
        }
    }
});