import { Component, Input, input } from '@angular/core';
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  NzFormControlComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzRadioComponent } from 'ng-zorro-antd/radio';

@Component({
  selector: 'app-input-radio',
  standalone: true,
  imports: [
    NzRadioComponent,
    FormsModule,
    ReactiveFormsModule,
    NzFormControlComponent,
    NzFormLabelComponent,
  ],
  templateUrl: './input-radio.html',
  styleUrl: './input-radio.less',
})
export class InputRadioComponent {
  readonly parentForm = input<FormGroup>(undefined);
  readonly controlName = input<string>(undefined);
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() label: string;
  readonly placeholder = input<number>(undefined);
  readonly width = input<string>('auto');
  readonly disabled = input<boolean>(false);

  isRequired() {
    return this.parentForm()
      .get(this.controlName())
      .hasValidator(Validators.required);
  }
}
