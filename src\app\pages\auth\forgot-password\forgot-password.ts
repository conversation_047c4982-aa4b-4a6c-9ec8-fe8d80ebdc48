import { UpperCasePipe } from '@angular/common';
import { Component, DestroyRef, OnInit, signal, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/http/auth';
import { MessageService } from '@core/services/utils/message';
import { CustomValidators } from '@core/validators/custom.validator';
import { TranslateModule } from '@ngx-translate/core';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { NzFormDirective } from 'ng-zorro-antd/form';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzTypographyComponent } from 'ng-zorro-antd/typography';

@Component({
  selector: 'app-forgot-password',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    InputGenericComponent,
    NzFormDirective,
    NzIconDirective,
    SimpleButtonComponent,
    UpperCasePipe,
    NzSpinComponent,
    NzTypographyComponent,
  ],
  templateUrl: './forgot-password.html',
  styleUrl: './forgot-password.less',
})
export class ForgotPasswordComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private authService = inject(AuthService);
  private messageService = inject(MessageService);
  private destroyRef = inject(DestroyRef);

  protected baseForm!: FormGroup;
  protected isLoading = signal<boolean>(false);
  protected forgotPassowrdError = signal<boolean>(false);

  constructor() {
    trackEvent('forgot_password_page');
  }

  ngOnInit(): void {
    this.baseForm = this.fb.group({
      email: [
        '',
        [
          Validators.required,
          Validators.minLength(5),
          Validators.maxLength(50),
          Validators.pattern(CustomValidators.emailRegex),
        ],
      ],
    });
  }

  onBackToLoginClick() {
    this.router.navigateByUrl('/auth/login');
  }

  onForgotPasswordClick() {
    this.isLoading.set(true);
    const email = this.baseForm.value.email;
    this.authService
      .forgotPassword(email)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: () => {
          this.messageService.addSuccessMessage('FORGOTPASSWORD.success');
          this.router.navigateByUrl('/auth/login');
          this.isLoading.set(false);
        },
        error: () => {
          this.forgotPassowrdError.set(true);
          this.isLoading.set(false);
          setTimeout(() => {
            this.forgotPassowrdError.set(false);
          }, 3000);
        },
      });
  }
}
