import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
import { IAdmin, IPartialAdmin } from '@models/interfaces/admin';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { CrudApiOperations } from '@models/interfaces/crud-api';
import { IRequestFilter, IRequestMeta } from '@shared/table/types/table.query';
import { Observable, of, switchMap, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AdminService implements CrudApiOperations<IAdmin, string> {
  // SERVICES
  private http = inject(HttpClient);

  // PROPERTIES
  private _baseAdminUrl: string = environment.api.admin;

  private _admin = signal<IAdmin | undefined>(undefined);
  public readonly admin$ = this._admin.asReadonly();

  public setAdmin(admin: IAdmin) {
    this._admin.set(admin);
  }

  create(admin: IAdmin) {
    return this.http.post<IBaseResponse<IAdmin>>(
      `${this._baseAdminUrl}`,
      admin,
    );
  }

  getAdminList() {
    return this.http.get<IBaseResponse<IPartialAdmin[]>>(
      `${this._baseAdminUrl}`,
    );
  }

  updateCustomer(adminId: string, admin: IAdmin) {
    return this.http.put<IBaseResponse<IAdmin>>(
      `${this._baseAdminUrl}/${adminId}`,
      admin,
    );
  }

  delete(adminId: string) {
    return this.http.delete<void>(`${this._baseAdminUrl}/${adminId}`);
  }

  readOne(adminId: string) {
    return this.http
      .get<IBaseResponse<IAdmin>>(`${this._baseAdminUrl}/${adminId}`)
      .pipe(tap((value) => this.setAdmin(value.data!)));
  }

  getAdminProfile(id: string) {
    return this.http
      .get<IBaseResponse<IAdmin>>(`${this._baseAdminUrl}/${id}`)
      .pipe(
        tap((res) => {
          this.setAdmin(res.data!);
        }),
      );
  }

  updateIsEnableStatus(adminId: string, readCustomer?: boolean) {
    return this.http
      .patch<
        IBaseResponse<IAdmin>
      >(`${this._baseAdminUrl}/${adminId}/change-enable-status`, null)
      .pipe(switchMap(() => (readCustomer ? this.readOne(adminId) : of(true))));
  }

  search(
    meta: IRequestMeta,
    filter: IRequestFilter[],
  ): Observable<IBaseResponse<IAdmin[], IFindResponseMeta>> {
    return this.http.post<IBaseResponse<IAdmin[], IFindResponseMeta>>(
      `${this._baseAdminUrl}/search`,
      {
        meta,
        filter,
      },
    );
  }
}
