<app-table
  [tableLayoutSettings]="tableLayoutSettings()"
  [loading]="loading()"
  [data]="data()"
  [refresh]="refreshData()"
  (onRefreshTimerTick)="onQueryChange(tableQueryRequest)"
  [tableId]="'_adminTable'"
  (onQueryChange)="onQueryChange($event)"
>
</app-table>

<ng-template #tplId let-element="element" let-row="row">
  <a
    ellipsis
    nz-typography
    [nzEllipsis]="element"
    nz-tooltip
    [nzTooltipTitle]="element | translate"
    (click)="goToDetail(element)"
    >{{ element }}
  </a>
</ng-template>

<ng-template #tplEnableStatus let-element="element">
  <app-tag-enable-status [value]="element"></app-tag-enable-status>
</ng-template>

<ng-template #tplActiveStatus let-element="element">
  <app-tag-active-status [value]="element"></app-tag-active-status>
</ng-template>

<ng-template #tplEllipsisText let-element="element">
  <p
    ellipsis
    nz-typography
    [nzEllipsis]="element"
    nz-tooltip
    [nzTooltipTitle]="element | translate"
  >
    {{ element }}
  </p>
</ng-template>
