import { Ng<PERSON><PERSON>, NgStyle } from '@angular/common';
import { Component, inject, Input, input, output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ResizeService } from '@core/services/utils/resize';
import { TranslateModule } from '@ngx-translate/core';
import {
  NzButtonComponent,
  NzButtonSize,
  NzButtonType,
} from 'ng-zorro-antd/button';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';

@Component({
  selector: 'app-simple-button',
  standalone: true,
  imports: [
    NzButtonComponent,
    NzIconDirective,
    TranslateModule,
    NzTooltipDirective,
    NgStyle,
    NgClass,
  ],
  templateUrl: './simple-button.html',
  styleUrl: './simple-button.less',
})
export class SimpleButtonComponent {
  private resizeService = inject(ResizeService);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input() title: string;
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() icon: string;
  readonly type = input<NzButtonType>('primary');
  readonly iconOnly = input<boolean>(false);
  readonly danger = input<boolean>(false);
  readonly breakpoint = input<number>(1200);
  readonly autoMinify = input<boolean>(true);
  readonly disabled = input<boolean>(undefined);
  readonly tooltipPlacement = input<
    | 'top'
    | 'left'
    | 'right'
    | 'bottom'
    | 'topLeft'
    | 'topRight'
    | 'bottomLeft'
    | 'bottomRight'
    | 'leftTop'
    | 'leftBottom'
    | 'rightTop'
    | 'rightBottom'
  >('top');
  readonly style = input<{
    [key: string]: any;
  }>(undefined);
  readonly iconStyle = input<{
    [key: string]: any;
  }>(undefined);
  readonly iconTheme = input<'outline' | 'fill' | 'twotone'>('outline');
  readonly size = input<NzButtonSize>('default');

  readonly onButtonClick = output<void>();

  protected minified: boolean = false;

  constructor() {
    this.resizeService
      .subscribe()
      .pipe(takeUntilDestroyed())
      .subscribe(() => {
        this.checkIfMinified();
      });
  }

  ngOnInit(): void {
    this.checkIfMinified();
  }

  private checkIfMinified() {
    if (this.iconOnly()) {
      this.minified = true;
      this.title = null;
    } else {
      if (this.autoMinify())
        this.minified = window.innerWidth < this.breakpoint();
    }
  }
}
